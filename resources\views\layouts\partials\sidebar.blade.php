<div class="principal-welcome">
    <!-- Principal Card -->
    <div class="principal-card">
        <div class="principal-image">
            <img src="{{ principal_image_url($principalData ? $principalData->picture : null) }}"
                alt="{{ $principalData && $principalData->user ? $principalData->user->principal : 'Kepal<PERSON> Sekolah' }}"
                class="img-fluid">
        </div>
        <div class="principal-info">
            <h4 class="principal-name">
                {{ $principalData && $principalData->user ? strtoupper($principalData->user->principal) : 'ANTON SOFYAN' }}
            </h4>
            <p class="principal-title">- Kepala Sekolah -</p>
            <p class="principal-message">
                @if ($principalData && $principalData->introduction)
                    {{ excerpt($principalData->introduction, 200) }}
                @else
                    Assalamu'alaikum warahmatullahi wabarakatuh, <PERSON><PERSON> sejahtera bagi kita semua, <PERSON> say<PERSON> hormati,
                    <PERSON><PERSON><PERSON>/<PERSON><PERSON> guru, sta<PERSON> ka<PERSON>wan se<PERSON>, para orang tua/wali murid yang hadir, serta anak-anakku yang
                    saya banggakan...
                @endif
            </p>
            <a href="{{ route('principal.welcome') }}" class="btn btn-outline-secondary btn-sm">SELENGKAPNYA</a>
        </div>
    </div>

    <!-- Newsletter Subscription Section -->
    <div class="newsletter-section">
        <h4 class="newsletter-title">Berlangganan</h4>
        <div class="newsletter-form">
            <div class="input-group">
                <input type="email" class="form-control" placeholder="Email Address..." aria-label="Email Address">
                <button class="btn btn-primary" type="button">
                    <i class="fas fa-envelope"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Advertisement Slider Section -->
    <div class="ads-section">
        <h4 class="ads-title">Iklan</h4>
        <div id="adsCarousel" class="carousel slide" data-bs-ride="carousel" data-bs-interval="3000">
            <div class="carousel-inner">
                @if ($adsData && $adsData->count() > 0)
                    @foreach ($adsData as $ads)
                        <div class="carousel-item {{ $loop->first ? 'active' : '' }}">
                            <div class="ads-image">
                                @if ($ads->link && $ads->link !== '-')
                                    <a href="{{ $ads->link }}" target="_blank">
                                        <img src="{{ ads_image_url($ads->picture) }}"
                                            alt="Advertisement {{ $loop->iteration }}" class="img-fluid">
                                    </a>
                                @else
                                    <img src="{{ ads_image_url($ads->picture) }}"
                                        alt="Advertisement {{ $loop->iteration }}" class="img-fluid">
                                @endif
                            </div>
                        </div>
                    @endforeach
                @else
                    <!-- Default Ads when no data -->
                    <div class="carousel-item active">
                        <div class="ads-image">
                            <img src="https://mts-nurulhudadau.sch.id/media_library/banners/1.png" alt="Advertisement 1"
                                class="img-fluid">
                        </div>
                    </div>
                    <div class="carousel-item">
                        <div class="ads-image">
                            <img src="https://mts-nurulhudadau.sch.id/media_library/banners/1.png" alt="Advertisement 2"
                                class="img-fluid">
                        </div>
                    </div>
                    <div class="carousel-item">
                        <div class="ads-image">
                            <img src="https://mts-nurulhudadau.sch.id/media_library/banners/1.png" alt="Advertisement 3"
                                class="img-fluid">
                        </div>
                    </div>
                @endif
            </div>

            @if ($adsData && $adsData->count() > 1)
                <!-- Carousel Controls (only show if more than 1 ad) -->
                <button class="carousel-control-prev" type="button" data-bs-target="#adsCarousel" data-bs-slide="prev">
                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                    <span class="visually-hidden">Previous</span>
                </button>
                <button class="carousel-control-next" type="button" data-bs-target="#adsCarousel" data-bs-slide="next">
                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                    <span class="visually-hidden">Next</span>
                </button>
            @endif
        </div>
    </div>
</div>
