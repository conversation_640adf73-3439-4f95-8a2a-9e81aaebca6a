<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Activity extends Model
{
    protected $table = 'activity';

    protected $fillable = [
        'title',
        'type',
        'description',
        'startdate',
        'starttime',
        'enddate',
        'endtime',
        'place',
        'picture',
        'createddate',
        'createdby',
        'updateddate',
        'updatedby'
    ];

    // Disable Laravel's automatic timestamps since we use custom fields
    public $timestamps = false;

    /**
     * Relationship with User (msusers table) - user who created this activity
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'createdby');
    }

    /**
     * Relationship with TypeActivity - activity belongs to a type
     */
    public function typeActivity()
    {
        return $this->belongsTo(TypeActivity::class, 'type');
    }

    /**
     * Get formatted start date
     */
    public function getFormattedStartDateAttribute()
    {
        return date('d F Y', strtotime($this->startdate));
    }

    /**
     * Get formatted end date
     */
    public function getFormattedEndDateAttribute()
    {
        return date('d F Y', strtotime($this->enddate));
    }

    /**
     * Get formatted date range
     */
    public function getDateRangeAttribute()
    {
        if ($this->startdate === $this->enddate) {
            return $this->formatted_start_date;
        }
        return $this->formatted_start_date . ' - ' . $this->formatted_end_date;
    }

    /**
     * Get formatted time range
     */
    public function getTimeRangeAttribute()
    {
        return date('H:i', strtotime($this->starttime)) . ' - ' . date('H:i', strtotime($this->endtime));
    }

    /**
     * Get excerpt of description (first 150 characters)
     */
    public function getExcerptAttribute()
    {
        $content = strip_tags($this->description);
        return strlen($content) > 150 ? substr($content, 0, 150) . '...' : $content;
    }

    /**
     * Get activity document URL
     */
    public function getDocumentUrlAttribute()
    {
        if ($this->picture) {
            return activity_document_url($this->picture);
        }
        return null;
    }
}
