<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Announcement extends Model
{
    protected $table = 'msannouncement';

    protected $fillable = [
        'description',
        'createddate',
        'createdby',
        'updateddate',
        'updatedby'
    ];

    // Disable Laravel's automatic timestamps since we use custom fields
    public $timestamps = false;

    /**
     * Relationship with User (msusers table) - user who created this announcement
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'createdby');
    }

    /**
     * Relationship with AnnouncementDetail - one announcement has many details
     */
    public function details()
    {
        return $this->hasMany(AnnouncementDetail::class, 'announcementid');
    }

    /**
     * Get formatted created date
     */
    public function getFormattedDateAttribute()
    {
        return date('d F Y', strtotime($this->createddate));
    }

    /**
     * Get excerpt of description (first 150 characters)
     */
    public function getExcerptAttribute()
    {
        $content = strip_tags($this->description);
        return strlen($content) > 150 ? substr($content, 0, 150) . '...' : $content;
    }

    /**
     * Get classes related to this announcement
     */
    public function getClassesAttribute()
    {
        return $this->details()->with('class')->get()->pluck('class.name')->filter()->implode(', ');
    }
}
