<?php $__env->startSection('title', 'Pengumuman - ' . ($user && $user->name ? $user->name : 'MTS Nurul Huda Dau')); ?>

<?php $__env->startSection('page-header'); ?>
    <div class="article-detail-header">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>">Beranda</a></li>
                            <li class="breadcrumb-item">Kategori</li>
                            <li class="breadcrumb-item active" aria-current="page">Pengumuman</li>
                        </ol>
                    </nav>
                    <h1 class="article-title-main">Pengumuman</h1>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="article-detail-content">
        <div class="container">
            <!-- Announcements Content -->
            <div class="announcements-content">
                <?php if($announcementsData && $announcementsData->count() > 0): ?>
                    <div class="articles-section">
                        <?php $__currentLoopData = $announcementsData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $announcement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <!-- Announcement <?php echo e($loop->iteration); ?> -->
                            <div class="announcement-item">
                                <div class="announcement-header">
                                    <div class="announcement-icon">
                                        <i class="fas fa-bullhorn"></i>
                                    </div>
                                    <div class="announcement-meta">
                                        <span class="announcement-date"><?php echo e($announcement->formatted_date); ?></span>
                                        <span class="announcement-author">Oleh <?php echo e($announcement->creator ? $announcement->creator->name : 'Administrator'); ?></span>
                                    </div>
                                </div>
                                <div class="announcement-content">
                                    <div class="announcement-description">
                                        <?php echo $announcement->description; ?>

                                    </div>
                                    <?php if($announcement->classes): ?>
                                        <div class="announcement-classes">
                                            <strong>Untuk Kelas:</strong> <?php echo e($announcement->classes); ?>

                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>

                    <!-- Pagination -->
                    <?php if($announcementsData->hasPages()): ?>
                        <div class="pagination-wrapper mt-5">
                            <nav aria-label="Announcements pagination">
                                <ul class="pagination justify-content-center">
                                    
                                    <?php if($announcementsData->onFirstPage()): ?>
                                        <li class="page-item disabled">
                                            <span class="page-link">
                                                <i class="fas fa-chevron-left"></i>
                                                Previous
                                            </span>
                                        </li>
                                    <?php else: ?>
                                        <li class="page-item">
                                            <a class="page-link" href="<?php echo e($announcementsData->previousPageUrl()); ?>">
                                                <i class="fas fa-chevron-left"></i>
                                                Previous
                                            </a>
                                        </li>
                                    <?php endif; ?>

                                    
                                    <?php $__currentLoopData = $announcementsData->getUrlRange(1, $announcementsData->lastPage()); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $page => $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php if($page == $announcementsData->currentPage()): ?>
                                            <li class="page-item active">
                                                <span class="page-link"><?php echo e($page); ?></span>
                                            </li>
                                        <?php else: ?>
                                            <li class="page-item">
                                                <a class="page-link" href="<?php echo e($url); ?>"><?php echo e($page); ?></a>
                                            </li>
                                        <?php endif; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                    
                                    <?php if($announcementsData->hasMorePages()): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="<?php echo e($announcementsData->nextPageUrl()); ?>">
                                                Next
                                                <i class="fas fa-chevron-right"></i>
                                            </a>
                                        </li>
                                    <?php else: ?>
                                        <li class="page-item disabled">
                                            <span class="page-link">
                                                Next
                                                <i class="fas fa-chevron-right"></i>
                                            </span>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="no-announcements-content">
                        <div class="text-center py-5">
                            <i class="fas fa-bullhorn fa-5x text-muted mb-4"></i>
                            <h3 class="text-muted">Belum Ada Pengumuman</h3>
                            <p class="text-muted">Pengumuman sekolah akan ditampilkan di sini.</p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
    <style>
        /* Use existing articles-section styling from main CSS */
        .articles-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
        }

        /* Announcement items with dividers */
        .announcement-item {
            margin-bottom: 25px;
            padding-bottom: 25px;
            border-bottom: 1px solid #e9ecef;
        }

        .announcement-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .announcement-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            gap: 15px;
        }

        .announcement-icon {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            flex-shrink: 0;
        }

        .announcement-meta {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .announcement-date {
            font-weight: 600;
            color: #2c3e50;
            font-size: 1rem;
        }

        .announcement-author {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .announcement-content {
            padding-left: 65px;
        }

        .announcement-description {
            color: #495057;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .announcement-description p {
            margin-bottom: 10px;
        }

        .announcement-classes {
            background: #f8f9fa;
            padding: 10px 15px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            font-size: 0.9rem;
            color: #495057;
        }

        .announcement-classes strong {
            color: #2c3e50;
        }

        .pagination-wrapper {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 12px;
        }

        .pagination .page-link {
            border: 1px solid #dee2e6;
            color: #495057;
            padding: 10px 15px;
            margin: 0 2px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .pagination .page-link:hover {
            background: #e9ecef;
            border-color: #adb5bd;
            color: #495057;
        }

        .pagination .page-item.active .page-link {
            background: #007bff;
            border-color: #007bff;
            color: white;
        }

        .pagination .page-item.disabled .page-link {
            background: #f8f9fa;
            border-color: #dee2e6;
            color: #6c757d;
        }

        .no-announcements-content {
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .articles-section {
                padding: 20px;
                margin-bottom: 20px;
            }

            .announcement-item {
                margin-bottom: 20px;
                padding-bottom: 20px;
            }

            .announcement-header {
                gap: 10px;
            }

            .announcement-icon {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }

            .announcement-content {
                padding-left: 50px;
            }

            .announcement-date {
                font-size: 0.9rem;
            }

            .announcement-author {
                font-size: 0.8rem;
            }

            .pagination .page-link {
                padding: 8px 12px;
                font-size: 14px;
            }
        }
    </style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\smartschoolproject_schoolprofile\resources\views/announcements.blade.php ENDPATH**/ ?>