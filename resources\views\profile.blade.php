@extends('layouts.master')

@section('title', 'Profil <PERSON>')

@section('meta-description',
    'Profil lengkap ' .
    ($user && $user->name ? $user->name : 'MTs Nurul <PERSON>da <PERSON>u') .
    ' -
    <PERSON><PERSON><PERSON>, identitas, dan informasi sekolah.')

@section('breadcrumb')
    <div class="breadcrumb-section">
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('home') }}">Beranda</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Profil Sekolah</li>
                </ol>
            </nav>
        </div>
    </div>
@endsection

@section('content')
    <div class="article-detail-content">
        <div class="container">
            <div class="article-detail">
                <!-- School Profile Header -->
                <div class="profile-header">
                    <div class="row align-items-center">
                        <div class="col-md-4 text-center">
                            <div class="school-logo-large">
                                <img src="{{ logo_url($user && $user->logo ? $user->logo : null) }}"
                                    alt="Logo {{ $user && $user->name ? $user->name : 'MTs Nurul Huda Dau' }}"
                                    class="img-fluid">
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="school-identity">
                                <h1 class="school-name">
                                    {{ $user && $user->name ? strtoupper($user->name) : 'MTS NURUL HUDA DAU' }}</h1>
                                @if ($user && $user->slogan)
                                    <p class="school-slogan">{{ $user->slogan }}</p>
                                @endif
                                <div class="school-details">
                                    @if ($user && $user->npsn)
                                        <div class="detail-item">
                                            <strong>NPSN:</strong> {{ $user->npsn }}
                                        </div>
                                    @endif
                                    @if ($user && $user->schoolemail)
                                        <div class="detail-item">
                                            <strong>Email:</strong> {{ $user->schoolemail }}
                                        </div>
                                    @endif
                                    @if ($user && $user->phonenumber)
                                        <div class="detail-item">
                                            <strong>Telepon:</strong> {{ $user->phonenumber }}
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- School History -->
                @if ($user && $user->history)
                    <div class="profile-section">
                        <h3 class="section-title">Sejarah Sekolah</h3>
                        <div class="section-content">
                            <div class="history-content">
                                {!! $user->history !!}
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Principal Information -->
                @if ($principalData && $principalData->user)
                    <div class="profile-section">
                        <h3 class="section-title">Kepala Sekolah</h3>
                        <div class="section-content">
                            <div class="principal-info-card">
                                <div class="row align-items-center">
                                    <div class="col-md-3 text-center">
                                        <div class="principal-photo">
                                            <img src="{{ principal_image_url($principalData->picture) }}"
                                                alt="{{ $principalData->user->principal }}"
                                                class="img-fluid rounded-circle">
                                        </div>
                                    </div>
                                    <div class="col-md-9">
                                        <div class="principal-details">
                                            <h4 class="principal-name">{{ strtoupper($principalData->user->principal) }}
                                            </h4>
                                            <p class="principal-title">Kepala Sekolah</p>
                                            @if ($principalData->introduction)
                                                <p class="principal-intro">{{ excerpt($principalData->introduction, 300) }}
                                                </p>
                                                <a href="{{ route('principal.welcome') }}" class="btn btn-primary btn-sm">
                                                    <i class="fas fa-user me-2"></i>Selengkapnya
                                                </a>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Contact Information -->
                <div class="profile-section">
                    <h3 class="section-title">Informasi Kontak</h3>
                    <div class="section-content">
                        <div class="contact-grid">
                            @if ($user && $user->address)
                                <div class="contact-item address-item">
                                    <div class="contact-icon">
                                        <i class="fas fa-map-marker-alt"></i>
                                    </div>
                                    <div class="contact-details">
                                        <h5>Alamat</h5>
                                        <p>{{ $user->address }}</p>
                                    </div>
                                </div>
                            @endif

                            @if ($user && $user->phonenumber)
                                <div class="contact-item">
                                    <div class="contact-icon">
                                        <i class="fas fa-phone"></i>
                                    </div>
                                    <div class="contact-details">
                                        <h5>Telepon</h5>
                                        <p>{{ $user->phonenumber }}</p>
                                    </div>
                                </div>
                            @endif

                            @if ($user && $user->schoolemail)
                                <div class="contact-item">
                                    <div class="contact-icon">
                                        <i class="fas fa-envelope"></i>
                                    </div>
                                    <div class="contact-details">
                                        <h5>Email</h5>
                                        <p>{{ $user->schoolemail }}</p>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* Profile page specific styles - scoped to avoid conflicts */
        .article-detail-content .profile-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            background-image:
                radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            padding: 50px;
            border-radius: 20px;
            margin-bottom: 50px;
            box-shadow: 0 8px 30px rgba(0, 123, 255, 0.3);
            color: white;
            position: relative;
            overflow: hidden;
        }

        .article-detail-content .profile-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="50" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="30" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
            pointer-events: none;
        }

        .article-detail-content .school-logo-large img {
            max-width: 200px;
            height: auto;
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
        }

        .article-detail-content .school-identity .school-name {
            font-size: 2.5rem;
            font-weight: bold;
            color: white;
            margin-bottom: 10px;
            line-height: 1.2;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .article-detail-content .school-slogan {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.9);
            font-style: italic;
            margin-bottom: 25px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .article-detail-content .school-details .detail-item {
            margin-bottom: 12px;
            font-size: 1rem;
            color: rgba(255, 255, 255, 0.95);
        }

        .article-detail-content .school-details .detail-item strong {
            color: rgba(255, 255, 255, 0.8);
            min-width: 80px;
            display: inline-block;
            font-weight: 600;
        }

        .article-detail-content .profile-section {
            margin-bottom: 40px;
        }

        .article-detail-content .section-title {
            font-size: 1.8rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 35px;
            margin-top: 50px;
            padding: 0 40px 15px 40px;
            border-bottom: 3px solid #007bff;
            display: inline-block;
        }

        .article-detail-content .section-content {
            background: white;
            padding: 40px 50px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .article-detail-content .address-info {
            display: flex;
            align-items: flex-start;
            font-size: 1.1rem;
            color: #495057;
        }

        .article-detail-content .address-info i {
            color: #007bff;
            margin-top: 5px;
        }

        .article-detail-content .history-content {
            font-size: 1rem;
            line-height: 1.8;
            color: #495057;
            text-align: justify;
        }

        .article-detail-content .history-content h1,
        .article-detail-content .history-content h2,
        .article-detail-content .history-content h3 {
            color: #2c3e50;
            margin-top: 25px;
            margin-bottom: 15px;
        }

        .article-detail-content .history-content p {
            margin-bottom: 15px;
        }

        .article-detail-content .principal-info-card {
            background: #f8f9fa;
            padding: 40px 50px;
            border-radius: 10px;
            border-left: 5px solid #007bff;
        }

        .article-detail-content .principal-photo img {
            width: 120px;
            height: 120px;
            object-fit: cover;
            border: 4px solid #007bff;
        }

        .article-detail-content .principal-name {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .article-detail-content .principal-title {
            color: #007bff;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .article-detail-content .principal-intro {
            color: #495057;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .article-detail-content .contact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
        }

        /* Make address contact item full width */
        .article-detail-content .contact-item.address-item {
            grid-column: 1 / -1;
        }

        .article-detail-content .contact-item {
            display: flex;
            align-items: flex-start;
            padding: 30px 35px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #007bff;
        }

        .article-detail-content .contact-icon {
            background: #007bff;
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .article-detail-content .contact-details h5 {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .article-detail-content .contact-details p {
            color: #495057;
            margin: 0;
            line-height: 1.5;
        }

        /* Card styling */
        .article-detail-content .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            overflow: hidden;
            margin-bottom: 30px;
        }

        .article-detail-content .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .article-detail-content .card-body {
            padding: 30px;
        }

        .article-detail-content .card-header {
            padding: 20px 30px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-bottom: 1px solid #dee2e6;
        }

        /* First section title should not have top margin */
        .article-detail-content .section-title:first-of-type {
            margin-top: 0;
        }

        /* Responsive Design - Profile page specific */
        @media (max-width: 768px) {
            .article-detail-content .section-title {
                padding: 0 20px 15px 20px;
            }

            .article-detail-content .profile-header {
                padding: 25px;
                text-align: center;
            }

            .article-detail-content .school-identity .school-name {
                font-size: 2rem;
                margin-top: 20px;
            }

            .article-detail-content .school-logo-large img {
                max-width: 150px;
            }

            .article-detail-content .section-content {
                padding: 25px 30px;
            }

            .article-detail-content .principal-info-card {
                padding: 25px 30px;
            }

            .article-detail-content .contact-item {
                padding: 20px 25px;
            }

            .article-detail-content .contact-grid {
                grid-template-columns: 1fr;
            }

            .article-detail-content .principal-info-card {
                text-align: center;
            }

            .article-detail-content .principal-photo {
                margin-bottom: 20px;
            }
        }
    </style>
@endsection
