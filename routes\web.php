<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Models\User;
use App\Models\Principal;
use App\Models\News;
use App\Models\Ads;
use App\Models\Settings;

Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/hubungi-kami', [HomeController::class, 'contact'])->name('contact');
Route::post('/hubungi-kami', [HomeController::class, 'submitContact'])->name('contact.submit');
Route::get('/visi-dan-misi', [HomeController::class, 'visionMission'])->name('vision-mission');
Route::get('/profil', [HomeController::class, 'profile'])->name('profile');
Route::get('/sambutan-kepala-sekolah', [HomeController::class, 'principalWelcome'])->name('principal.welcome');
Route::get('/galeri/foto-kegiatan', [HomeController::class, 'galleryPhotos'])->name('gallery.photos');
Route::get('/galeri/video', [HomeController::class, 'galleryVideos'])->name('gallery.videos');
Route::get('/kategori/berita', [HomeController::class, 'newsCategory'])->name('news.category');
Route::get('/kategori/pengumuman', [HomeController::class, 'announcements'])->name('announcements');
Route::get('/artikel/{id}', [HomeController::class, 'articleDetail'])->name('article.detail');
Route::get('/teachers', [HomeController::class, 'teachers'])->name('teachers');

// Activities route
Route::get('/kategori/kegiatan', [HomeController::class, 'activities'])->name('activities');

// Test route to check data
Route::get('/test-data', function () {
    $user = User::where('email', '<EMAIL>')->first();
    $principalData = null;
    $newsData = collect();
    $adsData = collect();

    if ($user) {
        $principalData = Principal::where('createdby', $user->id)->with('user')->first();
        $newsData = News::where('createdby', $user->id)->with('creator')->orderBy('createddate', 'desc')->limit(3)->get();
        $adsData = Ads::where('createdby', $user->id)->active()->with('creator')->orderBy('createddate', 'desc')->get();
    }

    $vision = $user ? Settings::getSettingValue('vision', $user->id) : null;
    $mission = $user ? Settings::getSettingValue('mission', $user->id) : null;

    return response()->json([
        'user' => $user,
        'principal_data' => $principalData,
        'news_data' => $newsData,
        'ads_data' => $adsData,
        'principal_image_url' => principal_image_url($principalData ? $principalData->picture : null),
        'news_count' => $newsData->count(),
        'ads_count' => $adsData->count(),
        'contact_info' => [
            'address' => $user ? $user->address : null,
            'phone' => $user ? $user->phonenumber : null,
            'email' => $user ? $user->email : null,
            'school_email' => $user ? $user->schoolemail : null,
        ],
        'vision_mission' => [
            'vision' => $vision,
            'mission' => $mission,
        ]
    ]);
});
