@extends('layouts.master')

@section('title', $user && $user->name ? $user->name : 'MTS Nurul Huda Dau')

@section('page-header')
    @include('layouts.partials.hero-slider')
@endsection

@section('content')
    <div class="articles-section">
        <h3 class="section-title"><PERSON><PERSON><PERSON></h3>

        @if ($newsData && $newsData->count() > 0)
            @foreach ($newsData as $news)
                <!-- Article {{ $loop->iteration }} -->
                <div class="article-item">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="article-image">
                                <img src="{{ news_image_url($news->thumbnail) }}" alt="{{ $news->title }}" class="img-fluid">
                                <div class="news-category-badge">
                                    {{ $news->category ? $news->category->name : 'Umum' }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="article-content">
                                <h4 class="article-title">{{ $news->title }}</h4>
                                <p class="article-excerpt">{{ $news->excerpt }}</p>
                                <div class="article-meta">
                                    <span class="meta-date">{{ $news->formatted_date }}</span>
                                    <span class="meta-author">Oleh
                                        {{ $news->creator ? $news->creator->name : 'Administrator' }}</span>
                                    <a href="{{ route('article.detail', $news->id) }}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-search"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        @else
            <!-- Default Articles when no data -->
            <div class="article-item">
                <div class="row">
                    <div class="col-md-4">
                        <div class="article-image">
                            <img src="https://mts-nurulhudadau.sch.id/media_library/posts/medium/post_image.png"
                                alt="Sample Post 1" class="img-fluid">
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="article-content">
                            <h4 class="article-title">Sample Post 1</h4>
                            <p class="article-excerpt">Lorem ipsum dolor sit amet, consectetur
                                adipisicing
                                elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
                                Ut
                                enim ad minim veniam, quis nostrud ex</p>
                            <div class="article-meta">
                                <span class="meta-date">15/01/2023 21:23</span>
                                <span class="meta-author">Oleh Administrator</span>
                                <span class="meta-views">Dilihat 58 kali</span>
                                <a href="{{ route('article.detail', 1) }}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-search"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>
@endsection
