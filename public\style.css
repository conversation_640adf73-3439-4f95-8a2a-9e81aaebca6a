/* Custom CSS for MTS Nurul Huda Dau */

/* Top Header Styles */
.top-header {
    background: linear-gradient(135deg, #1e5799 0%, #2989d8 50%, #207cca 51%, #7db9e8 100%);
    color: white;
    padding: 15px 0;
    font-size: 14px;
}

.school-logo {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.school-name {
    font-size: 18px;
    font-weight: bold;
    margin: 0;
    letter-spacing: 1px;
}

.school-tagline {
    font-size: 12px;
    opacity: 0.9;
    font-style: italic;
}

.contact-info {
    font-size: 13px;
}

.contact-item {
    display: flex;
    align-items: center;
    color: white;
    text-decoration: none;
    transition: opacity 0.3s ease;
}

.contact-item:hover {
    opacity: 0.8;
    cursor: pointer;
}

.contact-item i {
    font-size: 14px;
    opacity: 0.9;
}

/* Main Navigation Styles */
.main-navbar {
    background-color: #1e5799;
    padding: 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.main-navbar .navbar-nav {
    justify-content: flex-start;
}

.main-navbar .nav-link {
    color: white !important;
    font-weight: 500;
    font-size: 14px;
    padding: 15px 20px !important;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.main-navbar .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #ffffff !important;
}

.main-navbar .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Home icon special styling */
.main-navbar .nav-item:first-child .nav-link {
    padding: 15px 25px !important;
}

.main-navbar .nav-item:first-child .nav-link i {
    font-size: 16px;
}

/* Dropdown Styles */
.main-navbar .dropdown-menu {
    background-color: #2989d8;
    border: none;
    border-radius: 0;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-top: 0;
}

.main-navbar .dropdown-item {
    color: white;
    padding: 10px 20px;
    font-size: 13px;
    transition: background-color 0.3s ease;
}

.main-navbar .dropdown-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}

/* Dropdown arrow styling */
.main-navbar .dropdown-toggle::after {
    margin-left: 8px;
    vertical-align: middle;
}

/* Navbar toggler for mobile */
.navbar-toggler {
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 4px 8px;
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Hero Section with Slider */
.hero-section {
    position: relative;
    min-height: 500px;
    overflow: hidden;
}

.hero-section .carousel {
    height: 500px;
}

.hero-section .carousel-item {
    height: 500px;
}

.hero-image {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom,
            rgba(74, 85, 104, 0.6) 0%,
            rgba(45, 55, 72, 0.7) 50%,
            rgba(30, 87, 153, 0.8) 100%);
    display: flex;
    align-items: center;
    padding: 60px 0;
}

.hero-content {
    color: white;
    z-index: 2;
}

.hero-title {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    animation: fadeInUp 1s ease-out;
}

.hero-subtitle {
    font-size: 1.1rem;
    margin-bottom: 30px;
    opacity: 0.95;
    line-height: 1.6;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    animation: fadeInUp 1s ease-out 0.3s both;
}

/* Carousel Indicators */
.hero-section .carousel-indicators {
    bottom: 80px;
    z-index: 3;
}

.hero-section .carousel-indicators button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin: 0 5px;
    background-color: rgba(255, 255, 255, 0.5);
    border: 2px solid rgba(255, 255, 255, 0.8);
}

.hero-section .carousel-indicators button.active {
    background-color: #ffc107;
    border-color: #ffc107;
}

/* Carousel Controls */
.hero-section .carousel-control-prev,
.hero-section .carousel-control-next {
    width: 5%;
    z-index: 3;
}

.hero-section .carousel-control-prev-icon,
.hero-section .carousel-control-next-icon {
    width: 30px;
    height: 30px;
    background-size: 100%;
}

/* Quote Section - Absolute Position at Bottom */
.quote-section {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(90deg, #1e5799 0%, #2989d8 100%);
    padding: 15px 0;
    z-index: 4;
}

.quote-content {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 20px;
    flex-wrap: wrap;
}

.quote-btn {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #000;
    font-weight: bold;
    padding: 8px 20px;
    border-radius: 5px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    text-decoration: none;
    font-size: 0.9rem;
}

.quote-btn:hover {
    background-color: #e0a800;
    border-color: #d39e00;
    color: #000;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.quote-text {
    color: white;
    font-size: 0.9rem;
    line-height: 1.4;
    opacity: 0.95;
    flex: 1;
    min-width: 300px;
}

.quote-text strong {
    color: #ffc107;
    font-weight: bold;
}

/* Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Main content */
.main-content {
    min-height: 500px;
    padding: 40px 0;
    background-color: #f8f9fa;
}

/* Articles Section */
.articles-section {
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.section-title {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 25px;
}

.article-item {
    margin-bottom: 25px;
    padding-bottom: 25px;
    border-bottom: 1px solid #e9ecef;
}

.article-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.article-image {
    position: relative;
}

.article-image img {
    border-radius: 6px;
    width: 100%;
    height: 180px;
    object-fit: cover;
}

.news-category-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: rgba(0, 123, 255, 0.9);
    color: white;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.article-content {
    padding-left: 20px;
}

.article-title {
    font-size: 1.2rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 12px;
    line-height: 1.4;
}

.article-excerpt {
    color: #6c757d;
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 15px;
}

.article-meta {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
    font-size: 0.85rem;
    color: #6c757d;
}

.article-meta span {
    display: flex;
    align-items: center;
}

.article-meta .btn {
    margin-left: auto;
    padding: 4px 8px;
}

/* Principal Welcome Section */
.principal-welcome {
    position: sticky;
    top: 20px;
}

.principal-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 25px;
    text-align: center;
}

.principal-image {
    margin-bottom: 20px;
}

.principal-image img {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #e9ecef;
}

.principal-name {
    font-size: 1.3rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 5px;
    letter-spacing: 0.5px;
}

.principal-title {
    color: #6c757d;
    font-style: italic;
    margin-bottom: 15px;
    font-size: 0.95rem;
}

.principal-message {
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.6;
    margin-bottom: 20px;
    text-align: justify;
}

.principal-card .btn {
    background-color: transparent;
    border: 1px solid #6c757d;
    color: #6c757d;
    font-size: 0.85rem;
    padding: 8px 20px;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.principal-card .btn:hover {
    background-color: #1e5799;
    border-color: #1e5799;
    color: white;
}

/* Newsletter Section */
.newsletter-section {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 25px;
    margin-top: 20px;
    margin-bottom: 20px;
}

.newsletter-title {
    font-size: 1.3rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 3px solid #1e5799;
    position: relative;
}

.newsletter-title::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 50%;
    height: 3px;
    background-color: #ffc107;
}

.newsletter-form .input-group {
    border: 2px solid #e9ecef;
    border-radius: 5px;
    overflow: hidden;
}

.newsletter-form .form-control {
    border: none;
    padding: 12px 15px;
    font-size: 0.95rem;
    background-color: #f8f9fa;
}

.newsletter-form .form-control:focus {
    box-shadow: none;
    background-color: white;
}

.newsletter-form .btn {
    background-color: #1e5799;
    border-color: #1e5799;
    padding: 12px 20px;
    border: none;
}

.newsletter-form .btn:hover {
    background-color: #2989d8;
    border-color: #2989d8;
}

/* Advertisement Section */
.ads-section {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 25px;
    margin-bottom: 20px;
}

.ads-title {
    font-size: 1.3rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 3px solid #1e5799;
    position: relative;
}

.ads-title::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 50%;
    height: 3px;
    background-color: #ffc107;
}

.ads-section .carousel {
    border-radius: 6px;
    overflow: hidden;
}

.ads-image img {
    width: 100%;
    height: 207px;
    object-fit: cover;
    border-radius: 6px;
}

.ads-section .carousel-control-prev,
.ads-section .carousel-control-next {
    width: 15%;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 0;
}

.ads-section .carousel-control-prev-icon,
.ads-section .carousel-control-next-icon {
    width: 20px;
    height: 20px;
}

/* Responsive Design */
@media (max-width: 991.98px) {
    .top-header {
        text-align: center;
    }

    .contact-info {
        justify-content: center !important;
        margin-top: 15px;
        flex-wrap: wrap;
    }

    .contact-item {
        margin: 5px 10px;
    }

    .school-info {
        justify-content: center;
    }

    .main-navbar .nav-link {
        border-right: none;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .main-navbar .nav-link:last-child {
        border-bottom: none;
    }

    /* Articles and Principal Section - Tablet */
    .articles-section {
        padding: 20px;
        margin-bottom: 20px;
    }

    .article-content {
        padding-left: 15px;
    }

    .principal-welcome {
        position: static;
        margin-top: 20px;
    }

    .principal-card {
        padding: 20px;
    }

    /* Newsletter and Ads Section - Tablet */
    .newsletter-section,
    .ads-section {
        padding: 20px;
        margin-top: 15px;
        margin-bottom: 15px;
    }

    .newsletter-title,
    .ads-title {
        font-size: 1.2rem;
    }
}

@media (max-width: 767.98px) {
    .school-name {
        font-size: 16px;
    }

    .school-tagline {
        font-size: 11px;
    }

    .contact-info {
        font-size: 12px;
    }

    .contact-item {
        margin: 3px 8px;
    }

    .main-navbar .nav-link {
        font-size: 13px;
        padding: 12px 15px !important;
    }

    /* Hero Section Mobile */
    .hero-section {
        min-height: 400px;
    }

    .hero-section .carousel {
        height: 400px;
    }

    .hero-section .carousel-item {
        height: 400px;
    }

    .hero-overlay {
        padding: 40px 0;
    }

    .hero-title {
        font-size: 1.8rem;
        margin-bottom: 15px;
    }

    .hero-subtitle {
        font-size: 1rem;
        margin-bottom: 25px;
    }

    .hero-section .carousel-indicators {
        bottom: 60px;
    }

    .quote-section {
        padding: 12px 0;
    }

    .quote-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .quote-btn {
        padding: 6px 15px;
        font-size: 0.8rem;
        align-self: flex-start;
    }

    .quote-text {
        font-size: 0.8rem;
        min-width: auto;
        width: 100%;
    }

    /* Articles and Principal Section - Mobile */
    .articles-section {
        padding: 15px;
        margin-bottom: 15px;
    }

    .section-title {
        font-size: 1.3rem;
        margin-bottom: 20px;
    }

    .article-item {
        margin-bottom: 20px;
        padding-bottom: 20px;
    }

    .article-content {
        padding-left: 0;
        margin-top: 15px;
    }

    .article-title {
        font-size: 1.1rem;
        margin-bottom: 10px;
    }

    .article-excerpt {
        font-size: 0.9rem;
        margin-bottom: 12px;
    }

    .article-meta {
        gap: 10px;
        font-size: 0.8rem;
    }

    .article-meta .btn {
        margin-left: 0;
        margin-top: 5px;
    }

    .principal-card {
        padding: 15px;
    }

    .principal-image img {
        width: 120px;
        height: 120px;
    }

    .principal-name {
        font-size: 1.1rem;
    }

    .principal-message {
        font-size: 0.85rem;
    }

    /* Newsletter and Ads Section - Mobile */
    .newsletter-section,
    .ads-section {
        padding: 15px;
        margin-top: 10px;
        margin-bottom: 10px;
    }

    .newsletter-title,
    .ads-title {
        font-size: 1.1rem;
        margin-bottom: 12px;
    }

    .newsletter-form .form-control {
        padding: 10px 12px;
        font-size: 0.9rem;
    }

    .newsletter-form .btn {
        padding: 10px 15px;
    }

    .ads-image img {
        height: 150px;
    }

    /* Footer Section - Mobile */
    .footer-section {
        padding: 30px 0 0 0;
        margin-top: 30px;
    }

    .footer-column {
        margin-bottom: 25px;
    }

    .footer-title {
        font-size: 1.1rem;
        margin-bottom: 15px;
    }

    .footer-description {
        font-size: 0.85rem;
        margin-bottom: 15px;
    }

    .footer-content .contact-item {
        margin-bottom: 12px;
        gap: 10px;
    }

    .contact-details strong {
        font-size: 0.85rem;
    }

    .contact-details span {
        font-size: 0.8rem;
    }

    .tags-container {
        gap: 8px;
    }

    .tag-item {
        padding: 6px 12px;
        font-size: 0.75rem;
    }

    .social-links {
        gap: 10px;
    }

    .social-link {
        font-size: 0.85rem;
        padding: 6px 0;
    }

    .footer-bottom {
        padding: 15px 0;
        text-align: center;
    }

    .powered-by {
        text-align: center;
        margin-top: 10px;
    }

    .copyright p,
    .powered-by p {
        font-size: 0.75rem;
    }
}

/* Footer Section */
.footer-section {
    background: linear-gradient(135deg, #1e5799 0%, #2989d8 100%);
    color: white;
    padding: 40px 0 0 0;
    margin-top: 40px;
}

.footer-column {
    margin-bottom: 30px;
}

.footer-title {
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid rgba(255, 255, 255, 0.3);
    position: relative;
}

.footer-description {
    font-size: 0.9rem;
    line-height: 1.6;
    margin-bottom: 20px;
    opacity: 0.9;
}

/* Contact Items */
.footer-content .contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    gap: 12px;
}

.footer-content .contact-item i {
    font-size: 1.1rem;
    margin-top: 2px;
    opacity: 0.8;
    width: 20px;
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.contact-details strong {
    font-size: 0.9rem;
    font-weight: bold;
}

.contact-details span {
    font-size: 0.85rem;
    opacity: 0.9;
}

/* Tags Section */
.tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.tag-item {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    text-decoration: none;
    font-size: 0.8rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.tag-item:hover {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    transform: translateY(-2px);
}

/* Social Links */
.social-links {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.social-link {
    display: flex;
    align-items: center;
    gap: 12px;
    color: white;
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    padding: 8px 0;
}

.social-link:hover {
    color: #ffc107;
    transform: translateX(5px);
}

.social-link i {
    font-size: 1.2rem;
    width: 20px;
    text-align: center;
}

/* Footer Bottom */
.footer-bottom {
    background-color: rgba(0, 0, 0, 0.2);
    padding: 20px 0;
    margin-top: 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.copyright p {
    margin: 0;
    font-size: 0.8rem;
    opacity: 0.8;
    line-height: 1.4;
}

.powered-by {
    text-align: right;
}

.powered-by p {
    margin: 0;
    font-size: 0.8rem;
    opacity: 0.8;
}

/* Additional hover effects */
.main-navbar .nav-item:hover .nav-link {
    transform: translateY(-1px);
}

/* Smooth transitions */
* {
    transition: all 0.3s ease;
}

/* Custom scrollbar for better aesthetics */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #1e5799;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #2989d8;
}

/* Contact Page Styles */
.contact-section {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.contact-section .section-title {
    color: #1e3c72;
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 3px solid #ffd700;
}

.contact-form-container {
    margin-top: 20px;
}

.contact-form .form-label {
    color: #333;
    font-weight: 500;
    margin-bottom: 8px;
}

.contact-form .form-control {
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.contact-form .form-control:focus {
    border-color: #1e3c72;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25);
}

.contact-form .btn-primary {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    border: none;
    padding: 12px 30px;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.contact-form .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(30, 60, 114, 0.3);
}

/* Vision Mission Page Styles */
.page-header {
    text-align: center;
    padding: 30px 20px;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: white;
    border-radius: 10px;
    margin-bottom: 30px;
}

.page-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 8px;
}

.page-subtitle {
    font-size: 1rem;
    opacity: 0.9;
    margin: 0;
}

.vision-mission-section {
    margin-bottom: 30px;
    padding-bottom: 25px;
    position: relative;
}

.vision-mission-section:last-child {
    border-bottom: none;
}

.vision-card,
.mission-card {
    border: none;
    box-shadow: 0 2px 10px rgba(30, 60, 114, 0.1);
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-left: 4px solid #1e3c72;
}

.vision-card:hover,
.mission-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(30, 60, 114, 0.2);
}

.vision-card .card-header {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: white;
    border: none;
    padding: 15px 25px;
}

.mission-card .card-header {
    background: linear-gradient(135deg, #1e5799 0%, #2989d8 100%);
    color: white;
    border: none;
    padding: 15px 25px;
}

/* Vision Mission Section Title */
.vision-mission-section .section-title {
    font-size: 1.4rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    color: white !important;
}

.vision-mission-section .section-title i {
    font-size: 1.2rem;
    color: white !important;
}

.vision-card .card-body,
.mission-card .card-body {
    padding: 25px;
}

.vision-content,
.mission-content {
    font-size: 1rem;
    line-height: 1.8;
    color: #444;
    text-align: justify;
    background: #f8f9fa;
    padding: 20px;
    border-radius: 6px;
    border-left: 3px solid #1e3c72;
}

.vision-content p,
.mission-content p {
    margin-bottom: 15px;
    font-weight: 400;
}

.vision-content:last-child,
.mission-content:last-child {
    margin-bottom: 0;
}

.vision-mission-section .text-muted {
    font-style: italic;
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    margin: 0;
}

@media (max-width: 768px) {
    .sidebar-content {
        padding-left: 0;
        margin-top: 30px;
    }

    .page-header {
        padding: 20px 15px;
        margin-bottom: 20px;
    }

    .page-title {
        font-size: 1.6rem;
    }

    .page-subtitle {
        font-size: 0.9rem;
    }

    .vision-mission-section .section-title {
        font-size: 1.2rem;
    }

    .vision-mission-section .section-title i {
        font-size: 1rem;
    }

    .vision-card .card-body,
    .mission-card .card-body {
        padding: 20px;
    }

    .vision-card .card-header,
    .mission-card .card-header {
        padding: 12px 20px;
    }

    .vision-content,
    .mission-content {
        font-size: 0.95rem;
        line-height: 1.6;
    }

    .vision-mission-section {
        margin-bottom: 20px;
    }
}

/* Article Detail Page Styles */
.article-detail-header {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: white;
    padding: 40px 0 30px;
}

.breadcrumb {
    background: transparent;
    padding: 0;
    margin-bottom: 15px;
}

.breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: white;
}

.breadcrumb-item.active {
    color: white;
}

.breadcrumb-item+.breadcrumb-item::before {
    color: rgba(255, 255, 255, 0.6);
}

.article-meta-header {
    margin-bottom: 15px;
    font-size: 0.9rem;
    opacity: 0.9;
}

.article-meta-header .article-date,
.article-meta-header .article-author {
    margin-right: 20px;
}

.article-title-main {
    font-size: 2.2rem;
    font-weight: 700;
    line-height: 1.3;
    margin: 0;
}

.article-detail-content {
    padding: 20px 0;
}

.article-detail {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.article-image-main {
    width: 100%;
    height: 400px;
    overflow: hidden;
}

.article-image-main img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.article-content-main {
    padding: 30px;
    font-size: 1.1rem;
    line-height: 1.8;
    color: #333;
}

.article-content-main p {
    margin-bottom: 20px;
}

.article-content-main h1,
.article-content-main h2,
.article-content-main h3,
.article-content-main h4,
.article-content-main h5,
.article-content-main h6 {
    color: #1e3c72;
    margin-top: 30px;
    margin-bottom: 15px;
}

.article-tags {
    padding: 20px 30px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
}

.tag-label {
    font-weight: 600;
    color: #666;
    margin-right: 10px;
}

.tag-item {
    display: inline-block;
    background: #1e3c72;
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.85rem;
    margin-right: 8px;
    margin-bottom: 5px;
    text-decoration: none;
}

.tag-item:hover {
    background: #2a5298;
    color: white;
}

.article-share {
    padding: 25px 30px;
    border-top: 1px solid #e9ecef;
}

.article-share h5 {
    color: #1e3c72;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.share-buttons {
    display: flex;
    gap: 10px;
}

.share-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    color: white;
    text-decoration: none;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.share-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    color: white;
}

.share-btn.facebook {
    background: #3b5998;
}

.share-btn.twitter {
    background: #1da1f2;
}

.share-btn.whatsapp {
    background: #25d366;
}

.share-btn.copy-link {
    background: #6c757d;
}

.related-articles {
    padding: 30px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
}

.related-articles h4 {
    color: #1e3c72;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.related-article-item {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.related-article-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.related-article-image {
    height: 150px;
    overflow: hidden;
}

.related-article-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.related-article-content {
    padding: 15px;
}

.related-article-content h6 {
    margin-bottom: 8px;
    line-height: 1.4;
}

.related-article-content h6 a {
    color: #333;
    text-decoration: none;
    font-weight: 600;
}

.related-article-content h6 a:hover {
    color: #1e3c72;
}

/* Responsive Design for Article Detail */
@media (max-width: 768px) {
    .article-title-main {
        font-size: 1.8rem;
    }

    .article-meta-header {
        font-size: 0.85rem;
    }

    .article-meta-header .article-date,
    .article-meta-header .article-author {
        display: block;
        margin-right: 0;
        margin-bottom: 5px;
    }

    .article-image-main {
        height: 250px;
    }

    .article-content-main {
        padding: 20px;
        font-size: 1rem;
    }

    .article-tags,
    .article-share,
    .related-articles {
        padding: 20px;
    }

    .share-buttons {
        justify-content: center;
    }
}

/* Principal Welcome Page Styles */
.principal-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    margin-bottom: 0;
    padding: 40px;
    border-radius: 10px 10px 0 0;
    border-bottom: 3px solid #1e3c72;
}

.principal-photo img {
    width: 180px;
    height: 180px;
    object-fit: cover;
    border: 6px solid white;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.article-detail .principal-name {
    font-size: 1.8rem;
    font-weight: 700;
    color: #1e3c72;
    margin-bottom: 8px;
    letter-spacing: 0.5px;
}

.article-detail .principal-title {
    font-size: 1.1rem;
    color: #6c757d;
    margin-bottom: 8px;
    font-style: italic;
}

.principal-school-name {
    font-size: 1rem;
    color: #1e3c72;
    font-weight: 600;
    background: rgba(30, 60, 114, 0.1);
    padding: 8px 15px;
    border-radius: 20px;
    display: inline-block;
}

.principal-message-full {
    padding: 40px;
}

.message-title {
    font-size: 1.6rem;
    color: #1e3c72;
    margin-bottom: 25px;
    padding-bottom: 12px;
    border-bottom: 3px solid #1e3c72;
    display: inline-block;
    position: relative;
}

.message-title::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 60%;
    height: 3px;
    background: #ffc107;
}

.principal-message-full .message-content {
    font-size: 1.05rem;
    line-height: 1.7;
    color: #444;
    text-align: justify;
    background: #f8f9fa;
    padding: 30px;
    border-radius: 8px;
    border-left: 4px solid #1e3c72;
}

.principal-message-full .message-content p {
    margin-bottom: 18px;
}

.principal-message-full .message-content p:first-child {
    font-weight: 600;
    color: #1e3c72;
}

.principal-message-full .message-content strong {
    color: #1e3c72;
    font-weight: 700;
}

.back-button-section {
    margin-top: 30px;
    padding: 30px 40px;
    background: #f8f9fa;
    border-radius: 0 0 10px 10px;
    text-align: center;
    border-top: 1px solid #e9ecef;
}

.back-button-section .btn {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    border: none;
    padding: 12px 30px;
    font-weight: 600;
    border-radius: 25px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(30, 60, 114, 0.3);
}

.back-button-section .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(30, 60, 114, 0.4);
}

/* Gallery Photos Page Styles */
.gallery-content {
    padding: 20px 0;
}

.gallery-item {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    background: white;
}

.gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.gallery-image {
    position: relative;
    height: 250px;
    overflow: hidden;
    cursor: pointer;
}

.gallery-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-image img {
    transform: scale(1.05);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.7) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    padding: 20px;
    pointer-events: none;
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-overlay-content {
    text-align: center;
    color: white;
}

.gallery-overlay-content i {
    font-size: 2rem;
    margin-bottom: 10px;
    display: block;
}

.gallery-date {
    font-size: 0.9rem;
    margin: 0;
    opacity: 0.9;
}

.no-gallery-content {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
    margin: 40px 0;
}

/* Image Overlay Styles */
.image-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 9999;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    cursor: pointer;
}

.image-overlay.active {
    opacity: 1;
}

.overlay-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: default;
}

.overlay-content img {
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

.image-overlay.active .overlay-content img {
    transform: scale(1);
}

.close-btn {
    position: absolute;
    top: -40px;
    right: 0;
    color: white;
    font-size: 2.5rem;
    font-weight: bold;
    cursor: pointer;
    z-index: 10000;
    transition: color 0.3s ease;
    line-height: 1;
}

.close-btn:hover {
    color: #ffc107;
}

.overlay-controls {
    margin-top: 20px;
    display: flex;
    gap: 15px;
}

.overlay-btn {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 15px rgba(30, 60, 114, 0.3);
}

.overlay-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(30, 60, 114, 0.4);
    background: linear-gradient(135deg, #2a5298 0%, #1e3c72 100%);
}

.overlay-btn i {
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .gallery-image {
        height: 200px;
    }

    .gallery-overlay-content i {
        font-size: 1.5rem;
    }

    .gallery-date {
        font-size: 0.8rem;
    }

    /* Overlay responsive */
    .overlay-content {
        max-width: 95%;
        max-height: 95%;
    }

    .overlay-content img {
        max-height: 70vh;
    }

    .close-btn {
        font-size: 2rem;
        top: -35px;
    }

    .overlay-btn {
        padding: 10px 20px;
        font-size: 0.8rem;
    }

    .overlay-controls {
        margin-top: 15px;
    }
}