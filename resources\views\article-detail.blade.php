@extends('layouts.master')

@section('title', $article->title . ' - ' . ($user && $user->name ? $user->name : 'MTS Nurul Huda Dau'))

@section('page-header')
    <div class="article-detail-header">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}">Beranda</a></li>
                            <li class="breadcrumb-item active" aria-current="page">{{ $article->title }}</li>
                        </ol>
                    </nav>
                    <div class="article-meta-header">
                        <span class="article-date">{{ $article->formatted_date }}</span>
                        <span class="article-author">Ditulis oleh
                            {{ $article->creator ? $article->creator->name : 'Administrator' }}</span>
                    </div>
                    <h1 class="article-title-main">{{ $article->title }}</h1>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('content')
    <div class="content-section article-detail-section">
        <article class="article-detail">
            <!-- Article Image -->
            <div class="article-image-main">
                <img src="{{ news_image_url($article->thumbnail) }}" alt="{{ $article->title }}" class="img-fluid">
            </div>

            <!-- Article Content -->
            <div class="article-content-main">
                {!! nl2br($article->content) !!}
            </div>

            <!-- Article Tags/Categories -->
            <div class="article-tags">
                <span class="tag-label">Tags:</span>
                <span class="tag-item">Berita</span>
                <span class="tag-item">Sekolah</span>
            </div>

            <!-- Share Buttons -->
            <div class="article-share">
                <h5>Bagikan Artikel:</h5>
                <div class="share-buttons">
                    <a href="#" class="share-btn facebook" title="Share on Facebook">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#" class="share-btn twitter" title="Share on Twitter">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="share-btn whatsapp" title="Share on WhatsApp">
                        <i class="fab fa-whatsapp"></i>
                    </a>
                    <a href="#" class="share-btn copy-link" title="Copy Link">
                        <i class="fas fa-link"></i>
                    </a>
                </div>
            </div>

            <!-- Related Articles -->
            @if ($relatedArticles && $relatedArticles->count() > 0)
                <div class="related-articles">
                    <h4>Artikel Terkait</h4>
                    <div class="row">
                        @foreach ($relatedArticles as $related)
                            <div class="col-md-4 mb-3">
                                <div class="related-article-item">
                                    <div class="related-article-image">
                                        <img src="{{ news_image_url($related->thumbnail) }}" alt="{{ $related->title }}"
                                            class="img-fluid">
                                    </div>
                                    <div class="related-article-content">
                                        <h6><a href="{{ route('article.detail', $related->id) }}">{{ $related->title }}</a>
                                        </h6>
                                        <small class="text-muted">{{ $related->formatted_date }}</small>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
        </article>
    </div>
@endsection

@push('scripts')
    <script>
        // Copy link functionality
        document.querySelector('.copy-link').addEventListener('click', function(e) {
            e.preventDefault();
            navigator.clipboard.writeText(window.location.href).then(function() {
                alert('Link berhasil disalin!');
            });
        });

        // Social media share functionality
        document.querySelector('.share-btn.facebook').addEventListener('click', function(e) {
            e.preventDefault();
            window.open('https://www.facebook.com/sharer/sharer.php?u=' + encodeURIComponent(window.location.href),
                '_blank');
        });

        document.querySelector('.share-btn.twitter').addEventListener('click', function(e) {
            e.preventDefault();
            window.open('https://twitter.com/intent/tweet?url=' + encodeURIComponent(window.location.href) +
                '&text=' + encodeURIComponent(document.title), '_blank');
        });

        document.querySelector('.share-btn.whatsapp').addEventListener('click', function(e) {
            e.preventDefault();
            window.open('https://wa.me/?text=' + encodeURIComponent(document.title + ' ' + window.location.href),
                '_blank');
        });
    </script>
@endpush
