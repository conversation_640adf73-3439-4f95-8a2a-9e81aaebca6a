<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TypeActivity extends Model
{
    protected $table = 'typeactivity';

    protected $fillable = [
        'name',
        'createddate',
        'createdby',
        'updateddate',
        'updatedby'
    ];

    // Disable Laravel's automatic timestamps since we use custom fields
    public $timestamps = false;

    /**
     * Relationship with User (msusers table) - user who created this type
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'createdby');
    }

    /**
     * Relationship with Activity - one type has many activities
     */
    public function activities()
    {
        return $this->hasMany(Activity::class, 'type');
    }
}
