<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Settings extends Model
{
    protected $table = 'settings';

    protected $fillable = [
        'settings_name',
        'settings_value',
        'createddate',
        'createdby',
        'updateddate',
        'updatedby'
    ];

    // Disable Laravel's automatic timestamps since we use custom fields
    public $timestamps = false;

    /**
     * Get the user who created this setting
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'createdby');
    }

    /**
     * Get setting value by name and user
     */
    public static function getSettingValue($settingName, $userId)
    {
        $setting = self::where('settings_name', $settingName)
            ->where('createdby', $userId)
            ->first();

        return $setting ? $setting->settings_value : null;
    }
}
