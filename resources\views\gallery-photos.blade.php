@extends('layouts.master')

@section('title', 'Galeri Foto Kegiatan - ' . ($user && $user->name ? $user->name : 'MTS Nurul Huda Dau'))

@section('page-header')
    <div class="article-detail-header">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}">Beranda</a></li>
                            <li class="breadcrumb-item">Gale<PERSON></li>
                            <li class="breadcrumb-item active" aria-current="page">Foto Kegiatan</li>
                        </ol>
                    </nav>
                    <h1 class="article-title-main">Galeri Foto Kegiatan</h1>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('content')
    <div class="article-detail-content">
        <div class="container">
            <div class="gallery-content">
                @if ($galleryData && $galleryData->count() > 0)
                    <div class="row g-4">
                        @foreach ($galleryData as $gallery)
                            <div class="col-lg-4 col-md-6 col-sm-12">
                                <div class="gallery-item">
                                    <div class="gallery-image">
                                        <img src="{{ $gallery->image_url }}" alt="Foto Kegiatan"
                                            class="img-fluid gallery-clickable" data-image-url="{{ $gallery->image_url }}"
                                            onclick="openImageOverlay('{{ $gallery->image_url }}')"
                                            style="cursor: pointer;">
                                        <div class="gallery-overlay">
                                            <div class="gallery-overlay-content">
                                                <i class="fas fa-search-plus"></i>
                                                <p class="gallery-date">{{ $gallery->formatted_date }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="no-gallery-content">
                        <div class="text-center py-5">
                            <i class="fas fa-images fa-5x text-muted mb-4"></i>
                            <h3 class="text-muted">Belum Ada Foto Kegiatan</h3>
                            <p class="text-muted">Foto kegiatan sekolah akan ditampilkan di sini.</p>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Image Overlay -->
    <div id="imageOverlay" class="image-overlay" onclick="closeImageOverlay()">
        <div class="overlay-content">
            <span class="close-btn" onclick="closeImageOverlay()">&times;</span>
            <img id="overlayImage" src="" alt="Foto Kegiatan">
            <div class="overlay-controls">
                <button class="overlay-btn" onclick="closeImageOverlay()">
                    <i class="fas fa-times"></i> Tutup
                </button>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        function openImageOverlay(imageUrl) {
            const overlay = document.getElementById('imageOverlay');
            const overlayImage = document.getElementById('overlayImage');

            if (!overlay || !overlayImage) {
                return;
            }

            overlayImage.src = imageUrl;
            overlay.style.display = 'flex';

            // Prevent body scroll
            document.body.style.overflow = 'hidden';

            // Add fade in animation
            setTimeout(() => {
                overlay.classList.add('active');
            }, 10);
        }

        function closeImageOverlay() {
            const overlay = document.getElementById('imageOverlay');

            overlay.classList.remove('active');

            // Restore body scroll
            document.body.style.overflow = 'auto';

            // Hide overlay after animation
            setTimeout(() => {
                overlay.style.display = 'none';
            }, 300);
        }

        // Close overlay when pressing Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeImageOverlay();
            }
        });

        // Initialize gallery when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Prevent overlay from closing when clicking on the image
            const overlayImage = document.getElementById('overlayImage');
            if (overlayImage) {
                overlayImage.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
            }
        });
    </script>
@endpush
