<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SchoolClass extends Model
{
    protected $table = 'msclass';

    protected $fillable = [
        'level',
        'name',
        'teacherid',
        'createddate',
        'createdby',
        'updateddate',
        'updatedby'
    ];

    // Disable Laravel's automatic timestamps since we use custom fields
    public $timestamps = false;

    /**
     * Relationship with User (msusers table) - user who created this class
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'createdby');
    }

    /**
     * Relationship with AnnouncementDetail - class has many announcement details
     */
    public function announcementDetails()
    {
        return $this->hasMany(AnnouncementDetail::class, 'classid');
    }
}
