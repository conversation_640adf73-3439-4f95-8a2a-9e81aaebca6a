<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AnnouncementDetail extends Model
{
    protected $table = 'msannouncementdetail';

    protected $fillable = [
        'announcementid',
        'classid',
        'createddate',
        'createdby'
    ];

    // Disable Laravel's automatic timestamps since we use custom fields
    public $timestamps = false;

    /**
     * Relationship with Announcement - detail belongs to announcement
     */
    public function announcement()
    {
        return $this->belongsTo(Announcement::class, 'announcementid');
    }

    /**
     * Relationship with Class - detail belongs to class
     */
    public function class()
    {
        return $this->belongsTo(SchoolClass::class, 'classid');
    }
}
