@extends('layouts.master')

@section('title', 'Kategori Berita - ' . ($user && $user->name ? $user->name : 'MTS Nurul Huda Dau'))

@section('page-header')
    <div class="article-detail-header">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}">Beranda</a></li>
                            <li class="breadcrumb-item">Kategori</li>
                            <li class="breadcrumb-item active" aria-current="page">Berita</li>
                        </ol>
                    </nav>
                    <h1 class="article-title-main">Kategori Berita</h1>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('content')
    <div class="article-detail-content">
        <div class="container">
            <!-- Category Tabs -->
            @if ($categoriesData && $categoriesData->count() > 0)
                <div class="category-tabs mb-4">
                    <ul class="nav nav-pills justify-content-center" id="categoryTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <a class="nav-link {{ $currentCategory === 'all' ? 'active' : '' }}"
                                href="{{ route('news.category', ['category' => 'all']) }}">
                                Semua
                            </a>
                        </li>
                        @foreach ($categoriesData as $category)
                            <li class="nav-item" role="presentation">
                                <a class="nav-link {{ $currentCategory == $category->id ? 'active' : '' }}"
                                    href="{{ route('news.category', ['category' => $category->id]) }}">
                                    {{ $category->name }}
                                </a>
                            </li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <!-- News Content -->
            <div class="news-content">
                @if ($newsData && $newsData->count() > 0)
                    <div class="articles-section">
                        @foreach ($newsData as $news)
                            <!-- Article {{ $loop->iteration }} -->
                            <div class="article-item">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="article-image">
                                            <img src="{{ $news->thumbnail_url }}" alt="{{ $news->title }}"
                                                class="img-fluid">
                                            <div class="news-category-badge">
                                                {{ $news->category ? $news->category->name : 'Umum' }}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-8">
                                        <div class="article-content">
                                            <h4 class="article-title">{{ $news->title }}</h4>
                                            <p class="article-excerpt">{{ $news->excerpt }}</p>
                                            <div class="article-meta">
                                                <span class="meta-date">{{ $news->formatted_date }}</span>
                                                <span class="meta-author">Oleh
                                                    {{ $news->creator ? $news->creator->name : 'Administrator' }}</span>
                                                <a href="{{ route('article.detail', $news->id) }}"
                                                    class="btn btn-primary btn-sm">
                                                    <i class="fas fa-search"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <!-- Pagination -->
                    @if ($newsData->hasPages())
                        <div class="pagination-wrapper mt-5">
                            <nav aria-label="News pagination">
                                <ul class="pagination justify-content-center">
                                    {{-- Previous Page Link --}}
                                    @if ($newsData->onFirstPage())
                                        <li class="page-item disabled">
                                            <span class="page-link">
                                                <i class="fas fa-chevron-left"></i>
                                                Previous
                                            </span>
                                        </li>
                                    @else
                                        <li class="page-item">
                                            <a class="page-link"
                                                href="{{ $newsData->appends(request()->query())->previousPageUrl() }}">
                                                <i class="fas fa-chevron-left"></i>
                                                Previous
                                            </a>
                                        </li>
                                    @endif

                                    {{-- Pagination Elements --}}
                                    @foreach ($newsData->appends(request()->query())->getUrlRange(1, $newsData->lastPage()) as $page => $url)
                                        @if ($page == $newsData->currentPage())
                                            <li class="page-item active">
                                                <span class="page-link">{{ $page }}</span>
                                            </li>
                                        @else
                                            <li class="page-item">
                                                <a class="page-link" href="{{ $url }}">{{ $page }}</a>
                                            </li>
                                        @endif
                                    @endforeach

                                    {{-- Next Page Link --}}
                                    @if ($newsData->hasMorePages())
                                        <li class="page-item">
                                            <a class="page-link"
                                                href="{{ $newsData->appends(request()->query())->nextPageUrl() }}">
                                                Next
                                                <i class="fas fa-chevron-right"></i>
                                            </a>
                                        </li>
                                    @else
                                        <li class="page-item disabled">
                                            <span class="page-link">
                                                Next
                                                <i class="fas fa-chevron-right"></i>
                                            </span>
                                        </li>
                                    @endif
                                </ul>
                            </nav>
                        </div>
                    @endif
                @else
                    <div class="no-news-content">
                        <div class="text-center py-5">
                            <i class="fas fa-newspaper fa-5x text-muted mb-4"></i>
                            <h3 class="text-muted">Belum Ada Berita</h3>
                            <p class="text-muted">
                                @if ($currentCategory === 'all')
                                    Belum ada berita yang dipublikasikan.
                                @else
                                    Belum ada berita untuk kategori ini.
                                @endif
                            </p>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
@endsection

@push('styles')
    <style>
        .category-tabs {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 30px;
        }

        .category-tabs .nav-pills .nav-link {
            background: white;
            color: #495057;
            border: 1px solid #dee2e6;
            margin: 0 5px;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .category-tabs .nav-pills .nav-link:hover {
            background: #e9ecef;
            border-color: #adb5bd;
            color: #495057;
        }

        .category-tabs .nav-pills .nav-link.active {
            background: #007bff;
            border-color: #007bff;
            color: white;
        }

        /* Use existing articles-section styling from main CSS */
        .articles-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
        }

        /* Article items with dividers like home page */
        .article-item {
            margin-bottom: 25px;
            padding-bottom: 25px;
            border-bottom: 1px solid #e9ecef;
        }

        .article-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .article-image {
            position: relative;
        }

        .article-image img {
            border-radius: 6px;
            width: 100%;
            height: 180px;
            object-fit: cover;
        }

        .news-category-badge {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 123, 255, 0.9);
            color: white;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .article-content {
            padding-left: 20px;
        }

        .article-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
            line-height: 1.4;
        }

        .article-excerpt {
            color: #6c757d;
            font-size: 0.95rem;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .article-meta {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .article-meta span {
            font-size: 0.85rem;
            color: #6c757d;
        }

        .meta-date,
        .meta-author,
        .meta-category {
            display: inline-block;
        }

        .pagination-wrapper {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 12px;
        }

        .pagination .page-link {
            border: 1px solid #dee2e6;
            color: #495057;
            padding: 10px 15px;
            margin: 0 2px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .pagination .page-link:hover {
            background: #e9ecef;
            border-color: #adb5bd;
            color: #495057;
        }

        .pagination .page-item.active .page-link {
            background: #007bff;
            border-color: #007bff;
            color: white;
        }

        .pagination .page-item.disabled .page-link {
            background: #f8f9fa;
            border-color: #dee2e6;
            color: #6c757d;
        }

        .no-news-content {
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .category-tabs .nav-pills {
                flex-wrap: wrap;
            }

            .category-tabs .nav-pills .nav-link {
                margin: 5px 2px;
                padding: 8px 15px;
                font-size: 14px;
            }

            .articles-section {
                padding: 20px;
                margin-bottom: 20px;
            }

            .article-content {
                padding-left: 0;
                margin-top: 15px;
            }

            .article-item {
                margin-bottom: 20px;
                padding-bottom: 20px;
            }

            .article-meta {
                gap: 10px;
            }

            .article-meta span {
                font-size: 0.8rem;
            }

            .pagination .page-link {
                padding: 8px 12px;
                font-size: 14px;
            }
        }
    </style>
@endpush
