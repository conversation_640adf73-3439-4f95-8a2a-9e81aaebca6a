<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Principal;
use App\Models\News;
use App\Models\Ads;
use App\Models\Slider;
use App\Models\Gallery;
use App\Models\CategoryNews;
use App\Models\Announcement;
use App\Models\Inbox;
use App\Models\Settings;
use App\Models\Activity;
use App\Models\TypeActivity;

class HomeController extends Controller
{
    /**
     * Display the home page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Get user <NAME_EMAIL>
        $user = User::where('email', '<EMAIL>')->first();

        $principalData = null;
        $newsData = collect();
        $adsData = collect();
        $sliderData = collect();

        if ($user) {
            // Get principal data where createdby = userid
            $principalData = Principal::where('createdby', $user->id)
                ->with('user') // Load the related user data (principal's info)
                ->first();

            // Get latest news where createdby = userid (limit to 3 articles)
            $newsData = News::where('createdby', $user->id)
                ->with('creator')
                ->orderBy('createddate', 'desc')
                ->limit(3)
                ->get();

            // Get active ads where createdby = userid
            $adsData = Ads::where('createdby', $user->id)
                ->active() // Use scope for active ads
                ->with('creator')
                ->orderBy('createddate', 'desc')
                ->get();

            // Get slider data where createdby = userid
            $sliderData = Slider::where('createdby', $user->id)
                ->with('creator')
                ->orderBy('createddate', 'desc')
                ->get();
        }

        return view('home', compact('principalData', 'user', 'newsData', 'adsData', 'sliderData'));
    }

    /**
     * Display the contact page.
     *
     * @return \Illuminate\View\View
     */
    public function contact()
    {
        // Get user <NAME_EMAIL>
        $user = User::where('email', '<EMAIL>')->first();

        $principalData = null;
        $adsData = collect();

        if ($user) {
            // Get principal data where createdby = userid
            $principalData = Principal::where('createdby', $user->id)
                ->with('user') // Load the related user data (principal's info)
                ->first();

            // Get active ads where createdby = userid
            $adsData = Ads::where('createdby', $user->id)
                ->active() // Use scope for active ads
                ->with('creator')
                ->orderBy('createddate', 'desc')
                ->get();
        }

        return view('contact', compact('principalData', 'user', 'adsData'));
    }

    /**
     * Handle contact form submission.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function submitContact(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'message' => 'required|string|max:1000',
        ]);

        // Get user <NAME_EMAIL> for createdby
        $user = User::where('email', '<EMAIL>')->first();

        // Save contact form data to inbox table
        Inbox::create([
            'name' => $request->name,
            'email' => $request->email,
            'message' => $request->message,
            'createddate' => now(),
            'createdby' => $user ? $user->id : null,
            'updateddate' => now(),
            'updatedby' => $user ? $user->id : null,
            'url' => null
        ]);

        return redirect()->route('contact')->with('success', 'Pesan Anda telah berhasil dikirim!');
    }

    /**
     * Display the vision and mission page.
     *
     * @return \Illuminate\View\View
     */
    public function visionMission()
    {
        // Get user <NAME_EMAIL>
        $user = User::where('email', '<EMAIL>')->first();

        $vision = null;
        $mission = null;
        $principalData = null;
        $adsData = collect();

        if ($user) {
            // Get vision from settings where settings_name = 'vision' and createdby = userid
            $vision = Settings::getSettingValue('vision', $user->id);

            // Get mission from settings where settings_name = 'mission' and createdby = userid
            $mission = Settings::getSettingValue('mission', $user->id);

            // Get principal data where createdby = userid
            $principalData = Principal::where('createdby', $user->id)
                ->with('user') // Load the related user data (principal's info)
                ->first();

            // Get active ads where createdby = userid
            $adsData = Ads::where('createdby', $user->id)
                ->active() // Use scope for active ads
                ->with('creator')
                ->orderBy('createddate', 'desc')
                ->get();
        }

        return view('vision-mission', compact('user', 'vision', 'mission', 'principalData', 'adsData'));
    }

    /**
     * Display the school profile page.
     *
     * @return \Illuminate\View\View
     */
    public function profile()
    {
        // Get user <NAME_EMAIL>
        $user = User::where('email', '<EMAIL>')->first();

        $principalData = null;
        $adsData = collect();

        if ($user) {
            // Get principal data where createdby = userid
            $principalData = Principal::where('createdby', $user->id)
                ->with('user') // Load the related user data (principal's info)
                ->first();

            // Get active ads where createdby = userid
            $adsData = Ads::where('createdby', $user->id)
                ->active() // Use scope for active ads
                ->with('creator')
                ->orderBy('createddate', 'desc')
                ->get();
        }

        return view('profile', compact('principalData', 'user', 'adsData'));
    }

    /**
     * Display the article detail page.
     *
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function articleDetail($id)
    {
        // Get user <NAME_EMAIL>
        $user = User::where('email', '<EMAIL>')->first();

        $article = null;
        $relatedArticles = collect();
        $principalData = null;
        $adsData = collect();

        if ($user) {
            // Get the specific article
            $article = News::where('id', $id)
                ->where('createdby', $user->id)
                ->with('creator')
                ->first();

            // If article not found, redirect to home
            if (!$article) {
                return redirect()->route('home')->with('error', 'Artikel tidak ditemukan.');
            }

            // Get related articles (exclude current article, limit to 3)
            $relatedArticles = News::where('createdby', $user->id)
                ->where('id', '!=', $id)
                ->with('creator')
                ->orderBy('createddate', 'desc')
                ->limit(3)
                ->get();

            // Get principal data where createdby = userid
            $principalData = Principal::where('createdby', $user->id)
                ->with('user') // Load the related user data (principal's info)
                ->first();

            // Get active ads where createdby = userid
            $adsData = Ads::where('createdby', $user->id)
                ->active() // Use scope for active ads
                ->with('creator')
                ->orderBy('createddate', 'desc')
                ->get();
        } else {
            return redirect()->route('home')->with('error', 'Data tidak ditemukan.');
        }

        return view('article-detail', compact('user', 'article', 'relatedArticles', 'principalData', 'adsData'));
    }

    /**
     * Display the principal welcome page.
     *
     * @return \Illuminate\View\View
     */
    public function principalWelcome()
    {
        // Get user <NAME_EMAIL>
        $user = User::where('email', '<EMAIL>')->first();

        $principalData = null;
        $adsData = collect();

        if ($user) {
            // Get principal data where createdby = userid
            $principalData = Principal::where('createdby', $user->id)
                ->with('user') // Load the related user data (principal's info)
                ->first();

            // Get active ads where createdby = userid
            $adsData = Ads::where('createdby', $user->id)
                ->active() // Use scope for active ads
                ->with('creator')
                ->orderBy('createddate', 'desc')
                ->get();
        }

        return view('principal-welcome', compact('principalData', 'user', 'adsData'));
    }

    /**
     * Display gallery photos page
     */
    public function galleryPhotos()
    {
        $user = User::where('email', '<EMAIL>')->first();
        $adsData = collect();
        $galleryData = collect();
        $principalData = null;

        if ($user) {
            // Get principal data for sidebar
            $principalData = Principal::where('createdby', $user->id)
                ->with('user')
                ->first();

            // Get ads data for sidebar
            $adsData = Ads::where('createdby', $user->id)
                ->active()
                ->with('creator')
                ->orderBy('createddate', 'desc')
                ->get();

            // Get gallery photos data
            $galleryData = Gallery::where('createdby', $user->id)
                ->photos()
                ->with('creator')
                ->orderBy('createddate', 'desc')
                ->get();
        }

        return view('gallery-photos', compact('galleryData', 'user', 'adsData', 'principalData'));
    }

    /**
     * Display gallery videos page
     */
    public function galleryVideos()
    {
        $user = User::where('email', '<EMAIL>')->first();
        $adsData = collect();
        $galleryData = collect();
        $principalData = null;

        if ($user) {
            // Get principal data for sidebar
            $principalData = Principal::where('createdby', $user->id)
                ->with('user')
                ->first();

            // Get ads data for sidebar
            $adsData = Ads::where('createdby', $user->id)
                ->active()
                ->with('creator')
                ->orderBy('createddate', 'desc')
                ->get();

            // Get gallery videos data
            $galleryData = Gallery::where('createdby', $user->id)
                ->videos()
                ->with('creator')
                ->orderBy('createddate', 'desc')
                ->get();
        }

        return view('gallery-videos', compact('galleryData', 'user', 'adsData', 'principalData'));
    }

    /**
     * Display news category page with pagination
     */
    public function newsCategory(Request $request)
    {
        $user = User::where('email', '<EMAIL>')->first();
        $adsData = collect();
        $newsData = collect();
        $categoriesData = collect();
        $principalData = null;
        $currentCategory = $request->get('category', 'all');

        if ($user) {
            // Get principal data for sidebar
            $principalData = Principal::where('createdby', $user->id)
                ->with('user')
                ->first();

            // Get ads data for sidebar
            $adsData = Ads::where('createdby', $user->id)
                ->active()
                ->with('creator')
                ->orderBy('createddate', 'desc')
                ->get();

            // Get all categories for tabs
            $categoriesData = CategoryNews::where('createdby', $user->id)
                ->orderBy('name', 'asc')
                ->get();

            // Get news data with pagination
            $newsQuery = News::where('createdby', $user->id)
                ->with(['category', 'creator'])
                ->orderBy('createddate', 'desc');

            // Filter by category if not 'all'
            if ($currentCategory !== 'all') {
                $newsQuery->where('categoryid', $currentCategory);
            }

            $newsData = $newsQuery->paginate(6); // 6 news per page
        }

        return view('news-category', compact('newsData', 'categoriesData', 'currentCategory', 'user', 'adsData', 'principalData'));
    }

    /**
     * Display announcements page with pagination
     */
    public function announcements(Request $request)
    {
        $user = User::where('email', '<EMAIL>')->first();
        $adsData = collect();
        $announcementsData = collect();
        $principalData = null;

        if ($user) {
            // Get principal data for sidebar
            $principalData = Principal::where('createdby', $user->id)
                ->with('user')
                ->first();

            // Get ads data for sidebar
            $adsData = Ads::where('createdby', $user->id)
                ->active()
                ->with('creator')
                ->orderBy('createddate', 'desc')
                ->get();

            // Get announcements data with pagination
            $announcementsData = Announcement::where('createdby', $user->id)
                ->with(['creator', 'details.class'])
                ->orderBy('createddate', 'desc')
                ->paginate(6); // 6 announcements per page
        }

        return view('announcements', compact('announcementsData', 'user', 'adsData', 'principalData'));
    }

    /**
     * Display activities page with type filtering and pagination
     */
    public function activities(Request $request)
    {
        // Get user <NAME_EMAIL>
        $user = User::where('email', '<EMAIL>')->first();

        // Get principal data for sidebar
        $principalData = null;
        if ($user) {
            $principalData = Principal::where('createdby', $user->id)
                ->with('user') // Load the related user data (principal's info)
                ->first();
        }

        // Get ads data for sidebar
        $adsData = null;
        if ($user) {
            $adsData = Ads::where('createdby', $user->id)
                ->active()
                ->with('creator')
                ->orderBy('createddate', 'desc')
                ->get();
        }

        // Get all activity types for tabs
        $activityTypes = TypeActivity::where('createdby', $user->id)
            ->orderBy('name', 'asc')
            ->get();

        // Get selected type from request
        $selectedType = $request->get('type');

        // Build query for activities
        $activitiesQuery = Activity::where('createdby', $user->id)
            ->with(['creator', 'typeActivity'])
            ->orderBy('startdate', 'desc')
            ->orderBy('starttime', 'desc');

        // Filter by type if selected
        if ($selectedType && $selectedType !== 'all') {
            $activitiesQuery->where('type', $selectedType);
        }

        // Get activities with pagination
        $activitiesData = $activitiesQuery->paginate(6); // 6 activities per page

        // Preserve query parameters in pagination links
        $activitiesData->appends($request->query());

        return view('activities', compact('activitiesData', 'activityTypes', 'selectedType', 'user', 'adsData', 'principalData'));
    }

    /**
     * Display the teachers page.
     *
     * @return \Illuminate\View\View
     */

    public function teachers()
    {
        // Get user <NAME_EMAIL>
        $user = User::where('email', '<EMAIL>')->first();

        $principalData = null;

        if ($user) {
            // Get principal data for sidebar
            $principalData = Principal::where('createdby', $user->id)
                ->with('user')
                ->first();
        }

        $adsData = null;


        if ($user) {
            // Get ads data for sidebar
            $adsData = Ads::where('createdby', $user->id)
                ->active()
                ->with('creator')
                ->orderBy('createddate', 'desc')
                ->get();
        }

        $teachersData = null;

        if ($user) {
            // Get teachers data
            $teachersData = User::where('role', 'guru')
                ->where('createdby', $user->id)
                ->get();
        }            

        return view('teachers', compact('teachersData', 'user', 'adsData', 'principalData'));
    }
}
