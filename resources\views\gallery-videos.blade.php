@extends('layouts.master')

@section('title', 'Galeri Video - ' . ($user && $user->name ? $user->name : 'MTS Nurul Huda Dau'))

@section('page-header')
    <div class="article-detail-header">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}">Beranda</a></li>
                            <li class="breadcrumb-item">Galeri</li>
                            <li class="breadcrumb-item active" aria-current="page">Video</li>
                        </ol>
                    </nav>
                    <h1 class="article-title-main">Galeri Video</h1>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('content')
    <div class="article-detail-content">
        <div class="container">
            <div class="gallery-content">
                @if ($galleryData && $galleryData->count() > 0)
                    <div class="row g-4">
                        @foreach ($galleryData as $gallery)
                            <div class="col-lg-4 col-md-6 col-sm-12">
                                <div class="gallery-item video-item">
                                    <div class="gallery-image">
                                        @if ($gallery->picture)
                                            <img src="{{ $gallery->thumbnail_url }}" alt="Video Thumbnail"
                                                class="img-fluid video-thumbnail" style="cursor: pointer;"
                                                onclick="openVideoLink('{{ $gallery->link }}')">
                                        @else
                                            <div class="video-placeholder" onclick="openVideoLink('{{ $gallery->link }}')"
                                                style="cursor: pointer;">
                                                <i class="fas fa-play-circle"></i>
                                                <span>Video</span>
                                            </div>
                                        @endif
                                        <div class="gallery-overlay">
                                            <div class="gallery-overlay-content">
                                                <i class="fas fa-play"></i>
                                                <p class="gallery-date">{{ $gallery->formatted_date }}</p>
                                            </div>
                                        </div>
                                        <div class="video-play-icon">
                                            <i class="fas fa-play-circle"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="no-gallery-content">
                        <div class="text-center py-5">
                            <i class="fas fa-video fa-5x text-muted mb-4"></i>
                            <h3 class="text-muted">Belum Ada Video</h3>
                            <p class="text-muted">Video kegiatan sekolah akan ditampilkan di sini.</p>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
@endsection

@push('styles')
    <style>
        .gallery-item {
            margin-bottom: 30px;
        }

        .video-item .gallery-image {
            position: relative;
            overflow: hidden;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .video-item .gallery-image:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .video-thumbnail {
            width: 100%;
            height: 250px;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .video-item .gallery-image:hover .video-thumbnail {
            transform: scale(1.05);
        }

        .video-placeholder {
            width: 100%;
            height: 250px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .video-placeholder i {
            font-size: 48px;
            margin-bottom: 10px;
            opacity: 0.9;
        }

        .video-placeholder:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
        }

        .video-play-icon {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 48px;
            color: white;
            opacity: 0.9;
            transition: all 0.3s ease;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
        }

        .video-item .gallery-image:hover .video-play-icon {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1.1);
        }

        .gallery-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.7) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .video-item .gallery-image:hover .gallery-overlay {
            opacity: 1;
        }

        .gallery-overlay-content {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            color: white;
        }

        .gallery-overlay-content i {
            font-size: 20px;
            margin-bottom: 8px;
            display: block;
        }

        .gallery-date {
            margin: 0;
            font-size: 14px;
            opacity: 0.9;
        }



        .no-gallery-content {
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {

            .video-thumbnail,
            .video-placeholder {
                height: 200px;
            }

            .video-play-icon {
                font-size: 36px;
            }

            .video-placeholder i {
                font-size: 36px;
            }
        }
    </style>
@endpush

@push('scripts')
    <script>
        function openVideoLink(url) {
            // Add protocol if missing
            if (!url.startsWith('http://') && !url.startsWith('https://')) {
                url = 'https://' + url;
            }
            window.open(url, '_blank');
        }

        // Initialize gallery when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Add hover effects for better UX
            const videoItems = document.querySelectorAll('.video-item');
            videoItems.forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });

                item.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
@endpush
