<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Inbox extends Model
{
    protected $table = 'inbox';

    protected $fillable = [
        'name',
        'email',
        'message',
        'createddate',
        'createdby',
        'updateddate',
        'updatedby',
        'url'
    ];

    // Disable <PERSON><PERSON>'s automatic timestamps since we use custom fields
    public $timestamps = false;

    /**
     * Get the user who created this inbox entry
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'createdby');
    }
}
