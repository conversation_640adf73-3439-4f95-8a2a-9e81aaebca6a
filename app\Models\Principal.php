<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Principal extends Model
{
    protected $table = 'msprincipal';

    protected $fillable = [
        'user_id',
        'picture',
        'introduction',
        'createddate',
        'createdby',
        'updateddate',
        'updatedby'
    ];

    // Disable <PERSON>vel's automatic timestamps since we use custom fields
    public $timestamps = false;

    /**
     * Relationship with User (msusers table) - user who created this principal record
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'createdby');
    }

    /**
     * Relationship with User (msusers table) - the principal user
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
