<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Gallery extends Model
{
    protected $table = 'msgallerys';

    protected $fillable = [
        'category',
        'picture',
        'link',
        'createddate',
        'createdby',
        'updateddate',
        'updatedby'
    ];

    // Disable Laravel's automatic timestamps since we use custom fields
    public $timestamps = false;

    /**
     * Relationship with User (msusers table) - user who created this gallery
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'createdby');
    }

    /**
     * Scope for photo galleries only
     */
    public function scopePhotos($query)
    {
        return $query->where('category', 'foto');
    }

    /**
     * Scope for video galleries only
     */
    public function scopeVideos($query)
    {
        return $query->where('category', 'video');
    }

    /**
     * Get formatted created date
     */
    public function getFormattedDateAttribute()
    {
        return date('d F Y', strtotime($this->createddate));
    }

    /**
     * Get gallery image URL
     */
    public function getImageUrlAttribute()
    {
        if ($this->category === 'foto' && $this->picture) {
            return gallery_image_url($this->picture);
        }
        return gallery_image_url();
    }

    /**
     * Get video thumbnail URL
     */
    public function getThumbnailUrlAttribute()
    {
        if ($this->category === 'video' && $this->picture) {
            return gallery_image_url($this->picture);
        }
        return null;
    }
}
