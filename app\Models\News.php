<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class News extends Model
{
    protected $table = 'msnews';

    protected $fillable = [
        'categoryid',
        'title',
        'thumbnail',
        'content',
        'createddate',
        'createdby',
        'updateddate',
        'updatedby'
    ];

    // Disable Laravel's automatic timestamps since we use custom fields
    public $timestamps = false;

    /**
     * Relationship with User (msusers table) - user who created this news
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'createdby');
    }

    /**
     * Relationship with CategoryNews - news belongs to a category
     */
    public function category()
    {
        return $this->belongsTo(CategoryNews::class, 'categoryid');
    }

    /**
     * Get excerpt of content (first 150 characters)
     */
    public function getExcerptAttribute()
    {
        $content = strip_tags($this->content);
        return strlen($content) > 150 ? substr($content, 0, 150) . '...' : $content;
    }

    /**
     * Get formatted created date
     */
    public function getFormattedDateAttribute()
    {
        return date('d F Y', strtotime($this->createddate));
    }

    /**
     * Get news thumbnail URL
     */
    public function getThumbnailUrlAttribute()
    {
        if ($this->thumbnail) {
            return news_image_url($this->thumbnail);
        }
        return news_image_url();
    }
}
