@extends('layouts.master')

@section('title', 'Kegiatan - ' . ($user && $user->name ? $user->name : 'MTS Nurul Huda Dau'))

@section('page-header')
    <div class="article-detail-header">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}">Beranda</a></li>
                            <li class="breadcrumb-item">Kategori</li>
                            <li class="breadcrumb-item active" aria-current="page">Kegiatan</li>
                        </ol>
                    </nav>
                    <h1 class="article-title-main">Agenda Kegiatan</h1>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('content')
    <div class="article-detail-content">
        <div class="container">
            <!-- Activity Type Tabs -->
            <div class="activity-tabs-wrapper mb-4">
                <div class="activity-tabs">
                    <a href="{{ route('activities') }}"
                        class="activity-tab {{ !$selectedType || $selectedType === 'all' ? 'active' : '' }}">
                        Semua Kegiatan
                    </a>
                    @foreach ($activityTypes as $type)
                        <a href="{{ route('activities', ['type' => $type->id]) }}"
                            class="activity-tab {{ $selectedType == $type->id ? 'active' : '' }}">
                            {{ $type->name }}
                        </a>
                    @endforeach
                </div>
            </div>

            <!-- Activities Content -->
            <div class="activities-content">
                @if ($activitiesData && $activitiesData->count() > 0)
                    <div class="articles-section">
                        @foreach ($activitiesData as $activity)
                            <!-- Activity {{ $loop->iteration }} -->
                            <div class="activity-item">
                                <div class="activity-header">
                                    <div class="activity-icon">
                                        <i class="fas fa-calendar-alt"></i>
                                    </div>
                                    <div class="activity-title-section">
                                        <h4 class="activity-title">{{ $activity->title }}</h4>
                                        @if ($activity->typeActivity)
                                            <div class="activity-type-badge">{{ $activity->typeActivity->name }}</div>
                                        @endif
                                    </div>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-meta">
                                        <div class="meta-item">
                                            <i class="fas fa-calendar"></i>
                                            <span>{{ $activity->date_range }}</span>
                                        </div>
                                        <div class="meta-item">
                                            <i class="fas fa-clock"></i>
                                            <span>{{ $activity->time_range }}</span>
                                        </div>
                                        <div class="meta-item">
                                            <i class="fas fa-map-marker-alt"></i>
                                            <span>{{ $activity->place }}</span>
                                        </div>
                                    </div>
                                    <p class="activity-excerpt">{{ $activity->excerpt }}</p>
                                    <div class="activity-actions">
                                        @if ($activity->document_url)
                                            <a href="{{ $activity->document_url }}" class="btn btn-outline-primary btn-sm"
                                                target="_blank">
                                                <i class="fas fa-download me-1"></i>
                                                Unduh Dokumen
                                            </a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <!-- Pagination -->
                    @if ($activitiesData->hasPages())
                        <div class="pagination-wrapper mt-5">
                            <nav aria-label="Activities pagination">
                                <ul class="pagination justify-content-center">
                                    {{-- Previous Page Link --}}
                                    @if ($activitiesData->onFirstPage())
                                        <li class="page-item disabled">
                                            <span class="page-link">
                                                <i class="fas fa-chevron-left"></i>
                                                Previous
                                            </span>
                                        </li>
                                    @else
                                        <li class="page-item">
                                            <a class="page-link" href="{{ $activitiesData->previousPageUrl() }}">
                                                <i class="fas fa-chevron-left"></i>
                                                Previous
                                            </a>
                                        </li>
                                    @endif

                                    {{-- Pagination Elements --}}
                                    @foreach ($activitiesData->getUrlRange(1, $activitiesData->lastPage()) as $page => $url)
                                        @if ($page == $activitiesData->currentPage())
                                            <li class="page-item active">
                                                <span class="page-link">{{ $page }}</span>
                                            </li>
                                        @else
                                            <li class="page-item">
                                                <a class="page-link" href="{{ $url }}">{{ $page }}</a>
                                            </li>
                                        @endif
                                    @endforeach

                                    {{-- Next Page Link --}}
                                    @if ($activitiesData->hasMorePages())
                                        <li class="page-item">
                                            <a class="page-link" href="{{ $activitiesData->nextPageUrl() }}">
                                                Next
                                                <i class="fas fa-chevron-right"></i>
                                            </a>
                                        </li>
                                    @else
                                        <li class="page-item disabled">
                                            <span class="page-link">
                                                Next
                                                <i class="fas fa-chevron-right"></i>
                                            </span>
                                        </li>
                                    @endif
                                </ul>
                            </nav>
                        </div>
                    @endif
                @else
                    <div class="no-activities-content">
                        <div class="text-center py-5">
                            <i class="fas fa-calendar-alt fa-5x text-muted mb-4"></i>
                            <h3 class="text-muted">Belum Ada Kegiatan</h3>
                            <p class="text-muted">
                                @if ($selectedType && $selectedType !== 'all')
                                    Belum ada kegiatan untuk kategori ini.
                                @else
                                    Agenda kegiatan sekolah akan ditampilkan di sini.
                                @endif
                            </p>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
@endsection

@push('styles')
    <style>
        /* Activity tabs styling */
        .activity-tabs-wrapper {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 30px;
        }

        .activity-tabs {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
        }

        .activity-tab {
            background: white;
            color: #495057;
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .activity-tab:hover {
            background: #e9ecef;
            color: #495057;
            text-decoration: none;
            border-color: #adb5bd;
        }

        .activity-tab.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .activity-tab.active:hover {
            background: #0056b3;
            color: white;
            border-color: #0056b3;
        }

        /* Use existing articles-section styling from main CSS */
        .articles-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
        }

        /* Activity items with dividers */
        .activity-item {
            margin-bottom: 25px;
            padding-bottom: 25px;
            border-bottom: 1px solid #e9ecef;
        }

        .activity-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .activity-header {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            margin-bottom: 15px;
        }

        .activity-icon {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            flex-shrink: 0;
        }

        .activity-title-section {
            flex: 1;
        }

        .activity-type-badge {
            display: inline-block;
            background: #28a745;
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-top: 5px;
        }

        .activity-content {
            padding-left: 65px;
        }

        .activity-title {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 15px;
            line-height: 1.4;
        }

        .activity-meta {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-bottom: 15px;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #6c757d;
            font-size: 0.9rem;
        }

        .meta-item i {
            width: 16px;
            color: #007bff;
        }

        .activity-excerpt {
            color: #495057;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .activity-actions .btn {
            font-size: 0.875rem;
        }

        .pagination-wrapper {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 12px;
        }

        .pagination .page-link {
            border: 1px solid #dee2e6;
            color: #495057;
            padding: 10px 15px;
            margin: 0 2px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .pagination .page-link:hover {
            background: #e9ecef;
            border-color: #adb5bd;
            color: #495057;
        }

        .pagination .page-item.active .page-link {
            background: #007bff;
            border-color: #007bff;
            color: white;
        }

        .pagination .page-item.disabled .page-link {
            background: #f8f9fa;
            border-color: #dee2e6;
            color: #6c757d;
        }

        .no-activities-content {
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .activity-tabs {
                justify-content: flex-start;
                overflow-x: auto;
                padding-bottom: 10px;
            }

            .activity-tab {
                flex-shrink: 0;
                padding: 10px 20px;
                font-size: 14px;
            }

            .articles-section {
                padding: 20px;
                margin-bottom: 20px;
            }

            .activity-item {
                margin-bottom: 20px;
                padding-bottom: 20px;
            }

            .activity-header {
                gap: 10px;
            }

            .activity-icon {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }

            .activity-content {
                padding-left: 50px;
            }

            .activity-meta {
                gap: 6px;
            }

            .meta-item {
                font-size: 0.8rem;
            }

            .pagination .page-link {
                padding: 8px 12px;
                font-size: 14px;
            }
        }
    </style>
@endpush
