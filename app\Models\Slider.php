<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Slider extends Model
{
    protected $table = 'msslider';

    protected $fillable = [
        'title',
        'subtitle',
        'picture',
        'createddate',
        'createdby',
        'updateddate',
        'updatedby'
    ];

    // Disable <PERSON>vel's automatic timestamps since we use custom fields
    public $timestamps = false;

    /**
     * Relationship with User (msusers table) - user who created this slider
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'createdby');
    }

    /**
     * Scope for active sliders (if there's an active status field)
     */
    public function scopeActive($query)
    {
        // Assuming there might be an 'ispublish' or similar field
        // If not, this can be removed or modified based on actual table structure
        return $query;
    }

    /**
     * Get formatted created date
     */
    public function getFormattedDateAttribute()
    {
        return $this->createddate ? date('d F Y', strtotime($this->createddate)) : '';
    }
}
