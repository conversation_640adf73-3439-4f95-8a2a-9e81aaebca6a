<?php

if (!function_exists('smartschool_asset')) {
    /**
     * Generate SmartSchool asset URL
     *
     * @param string $path
     * @return string
     */
    function smartschool_asset($path)
    {
        $baseUrl = config('smartschool.base_url');
        $mainProject = config('smartschool.main_project_path');

        return $baseUrl . '/' . $mainProject . $path;
    }
}

if (!function_exists('principal_image_url')) {
    /**
     * Generate principal image URL
     *
     * @param string|null $filename
     * @return string
     */
    function principal_image_url($filename = null)
    {
        if (!$filename) {
            return 'https://mts-nurulhudadau.sch.id/media_library/images/headmaster_photo.png';
        }

        return smartschool_asset(config('smartschool.uploads.principal') . $filename);
    }
}

if (!function_exists('news_image_url')) {
    /**
     * Generate news image URL
     *
     * @param string|null $filename
     * @return string
     */
    function news_image_url($filename = null)
    {
        if (!$filename) {
            return 'https://mts-nurulhudadau.sch.id/media_library/posts/medium/post_image.png';
        }

        return smartschool_asset(config('smartschool.uploads.news') . $filename);
    }
}

if (!function_exists('ads_image_url')) {
    /**
     * Generate ads image URL
     *
     * @param string|null $filename
     * @return string
     */
    function ads_image_url($filename = null)
    {
        if (!$filename) {
            return 'https://mts-nurulhudadau.sch.id/media_library/banners/1.png';
        }

        return smartschool_asset(config('smartschool.uploads.ads') . $filename);
    }
}

if (!function_exists('slider_image_url')) {
    /**
     * Generate slider image URL
     *
     * @param string|null $filename
     * @return string
     */
    function slider_image_url($filename = null)
    {
        if (!$filename) {
            return 'https://mts-nurulhudadau.sch.id/media_library/image_sliders/1.png';
        }

        return smartschool_asset('/uploads/slider/' . $filename);
    }
}



if (!function_exists('logo_url')) {
    /**
     * Generate logo URL
     *
     * @param string|null $filename
     * @return string
     */
    function logo_url($filename = null)
    {
        if (!$filename) {
            return 'http://localhost/smartschoolprojectabsensi/uploads/logo/01857143ce05722e44732a292d0643d5.png';
        }

        return 'http://localhost/smartschoolprojectabsensi/uploads/logo/' . $filename;
    }
}

if (!function_exists('excerpt')) {
    /**
     * Create excerpt from text
     *
     * @param string $text
     * @param int $limit
     * @return string
     */
    function excerpt($text, $limit = 150)
    {
        $text = strip_tags($text);
        if (strlen($text) <= $limit) {
            return $text;
        }

        return substr($text, 0, $limit) . '...';
    }
}

if (!function_exists('gallery_image_url')) {
    /**
     * Generate gallery image URL
     *
     * @param string|null $filename
     * @return string
     */
    function gallery_image_url($filename = null)
    {
        if (!$filename) {
            return 'https://mts-nurulhudadau.sch.id/media_library/gallery/default_gallery.png';
        }

        return 'http://localhost/smartschoolprojectabsensi/uploads/gallery/' . $filename;
    }
}

if (!function_exists('activity_document_url')) {
    /**
     * Generate activity document URL
     *
     * @param string|null $filename
     * @return string
     */
    function activity_document_url($filename = null)
    {
        if (!$filename) {
            return null;
        }

        return 'http://localhost/smartschoolprojectabsensi/uploads/activity/' . $filename;
    }
}
