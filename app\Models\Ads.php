<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Ads extends Model
{
    protected $table = 'msads';

    protected $fillable = [
        'link',
        'picture',
        'startdate',
        'enddate',
        'createddate',
        'createdby',
        'updateddate',
        'updatedby',
        'ispublish'
    ];

    // Disable Laravel's automatic timestamps since we use custom fields
    public $timestamps = false;

    /**
     * Relationship with User (msusers table) - user who created this ads
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'createdby');
    }

    /**
     * Scope for active ads (published and within date range)
     */
    public function scopeActive($query)
    {
        $today = date('Y-m-d');
        return $query->where('ispublish', 1)
            ->where(function ($q) use ($today) {
                $q->whereNull('startdate')
                    ->orWhere('startdate', '<=', $today);
            })
            ->where(function ($q) use ($today) {
                $q->whereNull('enddate')
                    ->orWhere('enddate', '>=', $today);
            });
    }
}
