<!-- Hero Section with Slider -->
<section class="hero-section">
    <div id="heroCarousel" class="carousel slide" data-bs-ride="carousel" data-bs-interval="5000">
        <div class="carousel-indicators">
            @if ($sliderData && $sliderData->count() > 0)
                @foreach ($sliderData as $slider)
                    <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="{{ $loop->index }}"
                        class="{{ $loop->first ? 'active' : '' }}" aria-current="{{ $loop->first ? 'true' : 'false' }}"
                        aria-label="Slide {{ $loop->iteration }}"></button>
                @endforeach
            @else
                <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="0" class="active"
                    aria-current="true" aria-label="Slide 1"></button>
                <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="1"
                    aria-label="Slide 2"></button>
                <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="2"
                    aria-label="Slide 3"></button>
            @endif
        </div>

        <div class="carousel-inner">
            @if ($sliderData && $sliderData->count() > 0)
                @foreach ($sliderData as $slider)
                    <div class="carousel-item {{ $loop->first ? 'active' : '' }}">
                        <div class="hero-image"
                            style="background-image: url('{{ slider_image_url($slider->picture) }}');">
                            <div class="hero-overlay">
                                <div class="container">
                                    <div class="row justify-content-center">
                                        <div class="col-lg-8 text-center">
                                            <div class="hero-content">
                                                <h1 class="hero-title">
                                                    {{ $slider->title ?? 'Selamat Datang di ' . ($user && $user->name ? $user->name : 'MTS Nurul Huda Dau') }}
                                                </h1>
                                                <p class="hero-subtitle">
                                                    {{ $slider->subtitle ?? 'Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua' }}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            @else
                <!-- Default Slides when no data -->
                <div class="carousel-item active">
                    <div class="hero-image" style="background-image: url('{{ slider_image_url() }}');">
                        <div class="hero-overlay">
                            <div class="container">
                                <div class="row justify-content-center">
                                    <div class="col-lg-8 text-center">
                                        <div class="hero-content">
                                            <h1 class="hero-title">Selamat Datang di
                                                {{ $user && $user->name ? $user->name : 'MTS Nurul Huda Dau' }}</h1>
                                            <p class="hero-subtitle">Membangun generasi yang berakhlak mulia dan
                                                berprestasi dalam bidang akademik maupun non-akademik
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="carousel-item">
                    <div class="hero-image" style="background-image: url('{{ slider_image_url() }}');">
                        <div class="hero-overlay">
                            <div class="container">
                                <div class="row justify-content-center">
                                    <div class="col-lg-8 text-center">
                                        <div class="hero-content">
                                            <h1 class="hero-title">Pendidikan Berkualitas</h1>
                                            <p class="hero-subtitle">Membangun generasi yang berakhlak mulia dan
                                                berprestasi dalam bidang akademik maupun non-akademik</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="carousel-item">
                    <div class="hero-image" style="background-image: url('{{ slider_image_url() }}');">
                        <div class="hero-overlay">
                            <div class="container">
                                <div class="row justify-content-center">
                                    <div class="col-lg-8 text-center">
                                        <div class="hero-content">
                                            <h1 class="hero-title">Fasilitas Lengkap</h1>
                                            <p class="hero-subtitle">Didukung dengan fasilitas modern dan tenaga
                                                pengajar yang profesional untuk mendukung proses pembelajaran</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>

        <!-- Carousel Controls -->
        <button class="carousel-control-prev" type="button" data-bs-target="#heroCarousel" data-bs-slide="prev">
            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
            <span class="visually-hidden">Previous</span>
        </button>
        <button class="carousel-control-next" type="button" data-bs-target="#heroCarousel" data-bs-slide="next">
            <span class="carousel-control-next-icon" aria-hidden="true"></span>
            <span class="visually-hidden">Next</span>
        </button>
    </div>

    <!-- Quote Section - Position Absolute at Bottom -->
    <div class="quote-section">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="quote-content">
                        <a href="#" class="btn btn-warning quote-btn">
                            <i class="fas fa-graduation-cap me-2"></i>
                            KUTIPAN
                        </a>
                        <span class="quote-text">
                            Agama tanpa ilmu pengetahuan adalah buta. Dan ilmu pengetahuan tanpa agama adalah
                            lumpuh.
                            <strong>Anonim</strong>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
